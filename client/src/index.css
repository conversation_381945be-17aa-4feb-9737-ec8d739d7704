@import "@blueprintjs/core/lib/css/blueprint.css";
@import "@blueprintjs/popover2/lib/css/blueprint-popover2.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes pulseBorder {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.animate-pulse-border {
  animation: pulseBorder 2s infinite;
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Patrón de transparencia para imágenes sin fondo */
  .bg-checkered {
    background-image:
      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  }

  .dark .bg-checkered {
    background-image:
      linear-gradient(45deg, #404040 25%, transparent 25%),
      linear-gradient(-45deg, #404040 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #404040 75%),
      linear-gradient(-45deg, transparent 75%, #404040 75%);
  }

  /* Chat message text wrapping utilities */
  .chat-message-text {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    white-space: pre-wrap;
    max-width: 100%;
    overflow: hidden;
  }

  /* Ensure proper text wrapping in all browsers */
  .overflow-wrap-anywhere {
    overflow-wrap: anywhere;
  }

  /* Force text to wrap in containers */
  .force-text-wrap {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    word-break: break-word !important;
    white-space: pre-wrap !important;
    max-width: 100% !important;
    min-width: 0 !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Inter", sans-serif;
  }
}

/* Polotno específico */
.canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* EMMA AI POLOTNO SIDEBAR STYLING - GLOBAL OVERRIDE */
/* Target ALL possible tab selectors with maximum specificity */
.bp4-tab,
.bp4-tabs .bp4-tab,
[role="tab"],
.polotno-side-panel .bp4-tab,
.polotno-side-panel [role="tab"] {
  background-color: #f8f9fa !important;
  background-image: none !important;
  border: 1px solid #e1e5e9 !important;
  color: #6c757d !important;
  transition: all 0.2s ease !important;
  margin: 2px !important;
  border-radius: 6px !important;
}

/* Hover states - ALL selectors */
.bp4-tab:hover,
.bp4-tabs .bp4-tab:hover,
[role="tab"]:hover,
.polotno-side-panel .bp4-tab:hover,
.polotno-side-panel [role="tab"]:hover {
  background-color: #e3f2fd !important;
  background-image: none !important;
  border-color: #3018ef !important;
  color: #3018ef !important;
  transform: translateY(-1px) !important;
}

/* Active/Selected states - EMMA BLUE */
.bp4-tab[aria-selected="true"],
.bp4-tabs .bp4-tab[aria-selected="true"],
[role="tab"][aria-selected="true"],
.polotno-side-panel .bp4-tab[aria-selected="true"],
.polotno-side-panel [role="tab"][aria-selected="true"],
.bp4-tab.bp4-tab-selected,
.bp4-tabs .bp4-tab.bp4-tab-selected {
  background: linear-gradient(135deg, #3018ef 0%, #4f46e5 100%) !important;
  background-image: linear-gradient(135deg, #3018ef 0%, #4f46e5 100%) !important;
  border-color: #3018ef !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(48, 24, 239, 0.3) !important;
}

/* Active hover states */
.bp4-tab[aria-selected="true"]:hover,
.bp4-tabs .bp4-tab[aria-selected="true"]:hover,
[role="tab"][aria-selected="true"]:hover,
.polotno-side-panel .bp4-tab[aria-selected="true"]:hover,
.polotno-side-panel [role="tab"][aria-selected="true"]:hover {
  background: linear-gradient(135deg, #2614d4 0%, #4338ca 100%) !important;
  background-image: linear-gradient(135deg, #2614d4 0%, #4338ca 100%) !important;
  transform: translateY(-1px) !important;
}

/* Icons styling - make sure they're visible */
.bp4-tab svg,
.bp4-tabs .bp4-tab svg,
[role="tab"] svg {
  filter: none !important;
  fill: currentColor !important;
}

.bp4-tab[aria-selected="true"] svg,
.bp4-tabs .bp4-tab[aria-selected="true"] svg,
[role="tab"][aria-selected="true"] svg {
  filter: brightness(0) invert(1) !important;
  fill: white !important;
}

/* Force override any inline styles */
.bp4-tab[style] {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
}

.bp4-tab[aria-selected="true"][style] {
  background: linear-gradient(135deg, #3018ef 0%, #4f46e5 100%) !important;
  color: white !important;
}
