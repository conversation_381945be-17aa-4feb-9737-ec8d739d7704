import {
  FileText,
  Briefcase,
  Book,
  Mail,
  Search,
  Megaphone,
  Video,
  BarChart3,
  BookOpen,
  Brain,
  BriefcaseBusiness,
  Building,
  Calendar,
  CalendarDays,
  Code,
  CopyCheck,
  FileQuestion,
  Globe,
  Image,
  Lightbulb,
  ListChecks,
  MessageSquare,
  Mic,
  PenTool,
  Router,
  Settings,
  Tag,
  Target,
  User,
  Users,
  Zap,
  Layout,
  Info,
  ShoppingBag,
  Check,
  Send,
  Bot,
  LayoutDashboard,
  FileCheck,
  FileSpreadsheet,
  HelpCircle,
  Newspaper,
  Film,
  Share2,
  Store,
  ArrowBigRightDash,
  Radio,
  BadgeDollarSign,
  MonitorSmartphone,
  Palette,
  LayoutList,
  LayoutTemplate,
  Mic2,
} from "lucide-react";

// Importar los formularios adicionales de los otros archivos
import contentFormQuestionsExtra from "./content-form-questions-extra";
import contentFormQuestionsExtra2 from "./content-form-questions-extra2";
import contentFormQuestionsExtra3 from "./content-form-questions-extra3";
import contentFormQuestionsExtra4 from "./content-form-questions-extra4";

// También importar las funciones para buscar tipos de contenido por ID
import {
  findContentSubtypeById,
  findContentTypeById,
  findParentContentType,
} from "./content-types-data";

export type QuestionFieldType =
  | "text"
  | "textarea"
  | "select"
  | "radio"
  | "checkbox"
  | "number"
  | "date"
  | "editor"
  | "url"
  | "tone"
  | "slider"
  | "keywords"
  | "audience";

export interface ContentFormQuestion {
  id: string;
  type: QuestionFieldType;
  label: string;
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  min?: number;
  max?: number;
  options?: { value: string; label: string }[];
  defaultValue?: any;
  icon?: React.ElementType;
  advanced?: boolean;
  section?: string;
}

export interface ContentFormSection {
  id: string;
  title: string;
  description?: string;
  icon?: React.ElementType;
}

export interface ContentFormData {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  sections: ContentFormSection[];
  questions: ContentFormQuestion[];
  agents: {
    id: string;
    name: string;
    avatar: string;
    role: string;
    description: string;
  }[];
  tips?: string[];
  examples?: {
    title: string;
    description: string;
    link?: string;
  }[];
}

// Definir los formularios base que estaban en este archivo
const baseContentFormData: Record<string, ContentFormData> = {
  // Documento Técnico (Whitepaper)
  whitepaper: {
    id: "whitepaper",
    title: "Documento Técnico (Whitepaper)",
    description:
      "Crea un documento técnico autorizado para establecer expertise en tu sector",
    icon: FileText,
    sections: [
      {
        id: "informacion-basica",
        title: "Información Básica",
        description: "Datos fundamentales para tu whitepaper",
        icon: Info,
      },
      {
        id: "contenido-tecnico",
        title: "Contenido Técnico",
        description: "Aspectos técnicos y enfoque especializado",
        icon: Brain,
      },
      {
        id: "estructura-formato",
        title: "Estructura y Formato",
        description: "Organización y presentación del documento",
        icon: Layout,
      },
    ],
    questions: [
      {
        id: "titulo-whitepaper",
        type: "text",
        label: "Título del documento técnico",
        placeholder:
          'Ej. "Implementación de blockchain en la cadena de suministro"',
        helperText: "Un título descriptivo que refleje el tema central",
        required: true,
        section: "informacion-basica",
      },
      {
        id: "tema-principal",
        type: "textarea",
        label: "Tema principal",
        placeholder:
          "Describe el problema o la tecnología que abordará el documento",
        helperText: "Define con precisión el alcance y enfoque",
        required: true,
        section: "informacion-basica",
      },
      {
        id: "industria-sector",
        type: "text",
        label: "Industria o sector",
        placeholder: "Ej. Tecnología financiera, Salud, Manufactura",
        helperText: "El sector donde se aplica este conocimiento técnico",
        required: true,
        section: "informacion-basica",
      },
      {
        id: "objetivos-documento",
        type: "textarea",
        label: "Objetivos del documento",
        placeholder:
          "Ej. Demostrar beneficios de la tecnología X, Resolver problema Y",
        helperText: "Qué quieres lograr con este documento técnico",
        required: true,
        section: "informacion-basica",
      },
      {
        id: "nivel-tecnico",
        type: "select",
        label: "Nivel técnico",
        options: [
          {
            value: "introductorio",
            label: "Introductorio - Conceptos generales",
          },
          { value: "intermedio", label: "Intermedio - Cierta especialización" },
          { value: "avanzado", label: "Avanzado - Para especialistas" },
          {
            value: "experto",
            label: "Experto - Para profesionales con conocimiento profundo",
          },
        ],
        helperText: "Determina la profundidad técnica del contenido",
        required: true,
        section: "contenido-tecnico",
      },
      {
        id: "terminologia-especifica",
        type: "textarea",
        label: "Terminología específica",
        placeholder: "Lista términos técnicos clave para incluir",
        helperText: "Conceptos y términos técnicos que deben aparecer",
        section: "contenido-tecnico",
      },
      {
        id: "enfoque-tecnico",
        type: "select",
        label: "Enfoque técnico principal",
        options: [
          {
            value: "problema-solucion",
            label: "Problema-Solución - Cómo resolver un desafío",
          },
          {
            value: "metodologia",
            label: "Metodología - Proceso técnico detallado",
          },
          {
            value: "investigacion",
            label: "Investigación - Hallazgos y resultados",
          },
          {
            value: "comparativo",
            label: "Comparativo - Análisis de diferentes tecnologías",
          },
          {
            value: "implementacion",
            label: "Implementación - Guía de aplicación práctica",
          },
        ],
        helperText: "Perspectiva técnica desde la que abordarás el tema",
        required: true,
        section: "contenido-tecnico",
      },
      {
        id: "datos-tecnicos",
        type: "checkbox",
        label: "Elementos técnicos a incluir",
        options: [
          { value: "graficos", label: "Gráficos y diagramas técnicos" },
          { value: "tablas", label: "Tablas comparativas" },
          { value: "codigo", label: "Fragmentos de código" },
          { value: "formulas", label: "Fórmulas o ecuaciones" },
          { value: "casos", label: "Casos de estudio técnicos" },
          {
            value: "bibliografia",
            label: "Referencias bibliográficas técnicas",
          },
        ],
        helperText: "Selecciona elementos para reforzar el contenido técnico",
        section: "contenido-tecnico",
      },
      {
        id: "extension-documento",
        type: "select",
        label: "Extensión aproximada",
        options: [
          { value: "breve", label: "Breve (5-8 páginas)" },
          { value: "estandar", label: "Estándar (8-15 páginas)" },
          { value: "extenso", label: "Extenso (15-25 páginas)" },
          { value: "completo", label: "Completo (25+ páginas)" },
        ],
        helperText: "Determina la longitud del documento técnico",
        required: true,
        section: "estructura-formato",
      },
      {
        id: "secciones-especificas",
        type: "checkbox",
        label: "Secciones específicas",
        options: [
          { value: "resumen-ejecutivo", label: "Resumen ejecutivo" },
          { value: "metodologia", label: "Metodología" },
          { value: "analisis", label: "Análisis de datos" },
          { value: "resultados", label: "Resultados" },
          { value: "recomendaciones", label: "Recomendaciones técnicas" },
          { value: "conclusiones", label: "Conclusiones" },
          { value: "apendices", label: "Apéndices técnicos" },
        ],
        helperText: "Selecciona las secciones a incluir en el documento",
        section: "estructura-formato",
      },
      {
        id: "estilo-formato",
        type: "select",
        label: "Estilo de formato",
        options: [
          {
            value: "academico",
            label: "Académico - Citación formal y estructura rigurosa",
          },
          {
            value: "corporativo",
            label: "Corporativo - Profesional y orientado a negocios",
          },
          {
            value: "tecnico",
            label: "Técnico - Enfocado en especificaciones y datos",
          },
        ],
        helperText: "Define el estilo de presentación del documento",
        required: true,
        section: "estructura-formato",
      },
    ],
    agents: [
      {
        id: "investigador-tecnico",
        name: "Dr. Ramírez",
        avatar: "/agents/investigador-tecnico.png",
        role: "Investigador Técnico",
        description:
          "Especialista en documentación técnica y científica con experiencia en I+D",
      },
      {
        id: "analista-datos",
        name: "Sofía Métricas",
        avatar: "/agents/analista-datos.png",
        role: "Analista de Datos",
        description:
          "Experta en análisis de datos y presentación de información técnica compleja",
      },
    ],
    tips: [
      "Incluye un resumen ejecutivo conciso al inicio del documento",
      "Utiliza gráficos y diagramas para ilustrar conceptos complejos",
      "Mantén un tono objetivo y basado en evidencias",
      "Incluye referencias bibliográficas para respaldar tus afirmaciones",
      "Considera añadir un glosario de términos técnicos",
    ],
    examples: [
      {
        title: "Whitepaper tecnológico",
        description:
          "Blockchain como solución para la trazabilidad en la industria alimentaria",
      },
      {
        title: "Documento de investigación",
        description:
          "Análisis de la implementación de IA en diagnósticos médicos",
      },
      {
        title: "Informe técnico industrial",
        description:
          "Optimización energética en procesos de manufactura: un enfoque sostenible",
      },
    ],
  },

  // Newsletter
  newsletter: {
    id: "newsletter",
    title: "Newsletter",
    description:
      "Crea boletines periódicos con contenido valioso para tu audiencia",
    icon: Mail,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de tu newsletter",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y enfoque",
        description: "A quién va dirigido y cuál es su propósito",
        icon: Target,
      },
      {
        id: "content",
        title: "Contenido y secciones",
        description: "Qué incluirás en esta edición",
        icon: Layout,
      },
      {
        id: "design",
        title: "Diseño y formato",
        description: "Elementos visuales y estructura",
        icon: Image,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "newsletter_name",
        type: "text",
        label: "Nombre del newsletter",
        placeholder: 'Ej. "Marketing Insights Weekly", "El Briefing Digital"',
        helperText: "Nombre recurrente que identifica tu boletín",
        required: true,
        section: "basics",
      },
      {
        id: "issue_title",
        type: "text",
        label: "Título de esta edición",
        placeholder:
          'Ej. "5 tendencias de IA que revolucionarán el marketing en 2025"',
        helperText: "Título específico para este número del newsletter",
        required: true,
        section: "basics",
      },
      {
        id: "sender_name",
        type: "text",
        label: "Nombre del remitente",
        placeholder: 'Ej. "Ana García", "Equipo de Marketing de EmpresaX"',
        helperText: "Quién envía el newsletter (persona o equipo)",
        required: true,
        section: "basics",
      },
      {
        id: "frequency",
        type: "select",
        label: "Frecuencia de envío",
        options: [
          { value: "daily", label: "Diaria" },
          { value: "weekly", label: "Semanal" },
          { value: "biweekly", label: "Quincenal" },
          { value: "monthly", label: "Mensual" },
          { value: "quarterly", label: "Trimestral" },
          { value: "special", label: "Edición especial (única)" },
        ],
        helperText: "Cada cuánto tiempo envías este newsletter",
        required: true,
        section: "basics",
      },

      // Sección: Audiencia y enfoque
      {
        id: "target_audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Define quiénes recibirán este newsletter",
        helperText: "Describe la demografía e intereses de tus suscriptores",
        required: true,
        section: "audience",
      },
      {
        id: "subscriber_relationship",
        type: "select",
        label: "Relación con los suscriptores",
        options: [
          { value: "leads", label: "Leads/Prospectos" },
          { value: "customers", label: "Clientes actuales" },
          { value: "mixed", label: "Mixta (leads y clientes)" },
          { value: "partners", label: "Socios/Distribuidores" },
          { value: "internal", label: "Equipo interno/Empleados" },
          { value: "community", label: "Comunidad/Seguidores" },
        ],
        helperText: "Tipo de relación con los destinatarios",
        required: true,
        section: "audience",
      },
      {
        id: "newsletter_purpose",
        type: "select",
        label: "Propósito principal",
        options: [
          {
            value: "inform",
            label: "Informativo - Compartir novedades y actualizaciones",
          },
          {
            value: "educate",
            label: "Educativo - Enseñar y compartir conocimiento",
          },
          {
            value: "engage",
            label: "Engagement - Fomentar interacción y comunidad",
          },
          {
            value: "entertain",
            label: "Entretenimiento - Contenido ameno y ligero",
          },
          {
            value: "sell",
            label: "Comercial - Promocionar productos/servicios",
          },
          {
            value: "nurture",
            label: "Nutrición de leads - Avanzar en el embudo de conversión",
          },
          { value: "mixed", label: "Mixto - Combinación de propósitos" },
        ],
        helperText: "Objetivo principal de este newsletter",
        required: true,
        section: "audience",
      },
      {
        id: "value_proposition",
        type: "textarea",
        label: "Propuesta de valor",
        placeholder:
          'Ej. "La información más reciente sobre marketing digital en solo 5 minutos de lectura"',
        helperText: "¿Qué valor único ofrece tu newsletter a los suscriptores?",
        required: true,
        section: "audience",
      },

      // Sección: Contenido y secciones
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo con el que te comunicarás",
        required: true,
        section: "content",
      },
      {
        id: "main_topic",
        type: "textarea",
        label: "Tema principal",
        placeholder: "Describe el tema central de esta edición",
        helperText: "Enfoque principal de este número del newsletter",
        required: true,
        section: "content",
      },
      {
        id: "content_sections",
        type: "checkbox",
        label: "Secciones a incluir",
        options: [
          { value: "intro", label: "Introducción personal/Editorial" },
          { value: "main_article", label: "Artículo/Contenido principal" },
          { value: "news", label: "Noticias del sector" },
          { value: "tips", label: "Consejos y trucos" },
          { value: "resources", label: "Recursos recomendados" },
          { value: "case_study", label: "Caso de estudio/Historia" },
          { value: "product_updates", label: "Actualizaciones de producto" },
          { value: "upcoming_events", label: "Próximos eventos" },
          { value: "q_and_a", label: "Preguntas y respuestas" },
          { value: "social_proof", label: "Testimonios/Menciones" },
          { value: "promotion", label: "Oferta o promoción" },
        ],
        helperText: "Selecciona las secciones que compondrán este newsletter",
        required: true,
        section: "content",
      },
      {
        id: "content_details",
        type: "textarea",
        label: "Detalles del contenido",
        placeholder:
          "Describe los aspectos específicos que quieres incluir en cada sección",
        helperText:
          "Proporciona información detallada sobre el contenido de cada sección",
        section: "content",
      },
      {
        id: "cta_primary",
        type: "text",
        label: "CTA principal",
        placeholder: 'Ej. "Descarga nuestra guía gratuita", "Reserva tu plaza"',
        helperText: "Llamada a la acción principal que incluirás",
        section: "content",
      },

      // Sección: Diseño y formato
      {
        id: "length",
        type: "select",
        label: "Longitud aproximada",
        options: [
          { value: "very_short", label: "Muy corto (300-500 palabras)" },
          { value: "short", label: "Corto (500-800 palabras)" },
          { value: "medium", label: "Medio (800-1200 palabras)" },
          { value: "long", label: "Largo (1200-2000 palabras)" },
          { value: "very_long", label: "Muy largo (2000+ palabras)" },
        ],
        helperText: "Extensión total estimada del newsletter",
        required: true,
        section: "design",
      },
      {
        id: "visual_elements",
        type: "checkbox",
        label: "Elementos visuales",
        options: [
          { value: "header_image", label: "Imagen de cabecera" },
          { value: "article_images", label: "Imágenes para artículos" },
          { value: "gifs", label: "GIFs animados" },
          { value: "infographics", label: "Infografías" },
          { value: "videos", label: "Enlaces a videos" },
          { value: "product_images", label: "Imágenes de producto" },
          { value: "social_icons", label: "Iconos de redes sociales" },
          { value: "dividers", label: "Separadores gráficos" },
        ],
        helperText: "Elementos visuales que enriquecerán tu newsletter",
        section: "design",
      },
      {
        id: "design_style",
        type: "select",
        label: "Estilo de diseño",
        options: [
          { value: "minimal", label: "Minimalista - Simple y limpio" },
          {
            value: "rich",
            label: "Rico en contenido - Múltiples secciones y elementos",
          },
          {
            value: "branded",
            label: "Marca fuerte - Elementos visuales de marca prominentes",
          },
          {
            value: "text_focused",
            label: "Enfocado en texto - Estilo tipo carta/mensaje",
          },
          {
            value: "magazine",
            label: "Estilo revista - Layout editorial sofisticado",
          },
        ],
        helperText: "Enfoque general del diseño del newsletter",
        section: "design",
      },
      {
        id: "responsive_considerations",
        type: "checkbox",
        label: "Consideraciones responsive",
        options: [
          { value: "mobile_friendly", label: "Diseño optimizado para móvil" },
          { value: "text_version", label: "Versión solo texto alternativa" },
          {
            value: "image_alt_text",
            label: "Textos alternativos para imágenes",
          },
          { value: "dark_mode", label: "Optimización para modo oscuro" },
        ],
        helperText:
          "Opciones para garantizar buena experiencia en diferentes dispositivos",
        section: "design",
      },
    ],
    agents: [
      {
        id: "editor-newsletter",
        name: "Patricia Editorial",
        avatar: "/agents/editor-newsletter.png",
        role: "Editora de Newsletters",
        description:
          "Especialista en crear newsletters con alto ratio de apertura y engagement",
      },
      {
        id: "experto-contenido",
        name: "Diego Contenido",
        avatar: "/agents/experto-contenido.png",
        role: "Experto en Contenido",
        description:
          "Especialista en curación de contenido y desarrollo de narrativas para audiencias específicas",
      },
    ],
    tips: [
      "Utiliza un asunto atractivo y personal para aumentar la tasa de apertura",
      "Mantén un diseño coherente entre ediciones para reforzar el reconocimiento de marca",
      "Segmenta tu audiencia para enviar contenido más relevante a cada grupo",
      "Incluye enlaces a tus redes sociales para fomentar la conexión multiplataforma",
      "Analiza las métricas para optimizar continuamente (tasas de apertura, clics, etc.)",
    ],
    examples: [
      {
        title: "Newsletter de tendencias de marketing",
        description:
          "Resumen semanal de las principales novedades del sector con análisis experto",
      },
      {
        title: "Boletín educativo de producto",
        description:
          "Edición mensual con consejos, casos de uso y actualizaciones de productos",
      },
      {
        title: "Revista digital de industria",
        description:
          "Publicación trimestral con artículos en profundidad y entrevistas a expertos",
      },
    ],
  },

  // Categoría: Email y Notificaciones - Mensaje Automatizado
  "auto-email": {
    id: "auto-email",
    title: "Mensaje Automatizado",
    description:
      "Crea emails automatizados que se envían en respuesta a acciones específicas",
    icon: Bot,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales del mensaje",
        icon: FileText,
      },
      {
        id: "trigger",
        title: "Disparador y contexto",
        description: "Qué acción desencadena este mensaje",
        icon: Zap,
      },
      {
        id: "content",
        title: "Contenido y elementos",
        description: "Qué incluirá el mensaje automatizado",
        icon: MessageSquare,
      },
      {
        id: "technical",
        title: "Aspectos técnicos",
        description: "Configuración y consideraciones técnicas",
        icon: Settings,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "email_name",
        type: "text",
        label: "Nombre del email interno",
        placeholder:
          'Ej. "Bienvenida nuevos suscriptores", "Abandono de carrito 24h"',
        helperText:
          "Nombre para identificar este mensaje en tu sistema (no lo verá el usuario)",
        required: true,
        section: "basics",
      },
      {
        id: "email_subject",
        type: "text",
        label: "Asunto del email",
        placeholder:
          'Ej. "Bienvenido/a a la familia de Empresa X", "¿Olvidaste algo en tu carrito?"',
        helperText: "Asunto que verá el destinatario en su bandeja de entrada",
        required: true,
        section: "basics",
      },
      {
        id: "sender_name",
        type: "text",
        label: "Nombre del remitente",
        placeholder: 'Ej. "María de Empresa X", "Soporte Técnico"',
        helperText: "Quién aparece como remitente del mensaje",
        required: true,
        section: "basics",
      },
      {
        id: "email_purpose",
        type: "select",
        label: "Propósito del email",
        options: [
          { value: "welcome", label: "Bienvenida - Primer contacto" },
          { value: "onboarding", label: "Onboarding - Guiar al usuario" },
          {
            value: "transactional",
            label: "Transaccional - Confirmar acción/compra",
          },
          {
            value: "reactivation",
            label: "Reactivación - Recuperar usuarios inactivos",
          },
          { value: "cart", label: "Carrito abandonado - Recuperar venta" },
          { value: "feedback", label: "Solicitud de feedback" },
          {
            value: "upsell",
            label: "Upsell/Cross-sell - Sugerir productos adicionales",
          },
          { value: "event", label: "Recordatorio de evento" },
          { value: "renewal", label: "Renovación de suscripción/servicio" },
        ],
        helperText: "Objetivo principal de este mensaje automatizado",
        required: true,
        section: "basics",
      },

      // Sección: Disparador y contexto
      {
        id: "trigger_action",
        type: "select",
        label: "Acción disparadora",
        options: [
          { value: "signup", label: "Registro/Suscripción" },
          { value: "purchase", label: "Compra realizada" },
          { value: "cart_abandon", label: "Abandono de carrito" },
          { value: "form_submit", label: "Envío de formulario" },
          { value: "page_visit", label: "Visita a página específica" },
          { value: "download", label: "Descarga de contenido" },
          { value: "inactivity", label: "Período de inactividad" },
          {
            value: "date",
            label: "Fecha específica (cumpleaños, aniversario)",
          },
          { value: "event_register", label: "Registro a evento" },
          { value: "custom", label: "Personalizado (especificar abajo)" },
        ],
        helperText: "Acción que desencadena el envío de este email",
        required: true,
        section: "trigger",
      },
      {
        id: "custom_trigger",
        type: "textarea",
        label: "Disparador personalizado",
        placeholder:
          "Describe la acción o evento específico que dispara este email",
        helperText: 'Completa solo si seleccionaste "Personalizado" arriba',
        advanced: true,
        section: "trigger",
      },
      {
        id: "timing",
        type: "select",
        label: "Momento de envío",
        options: [
          {
            value: "immediate",
            label: "Inmediato - Justo después de la acción",
          },
          { value: "hours", label: "Horas después - Especificar cantidad" },
          { value: "next_day", label: "Día siguiente (24h)" },
          {
            value: "days",
            label: "Varios días después - Especificar cantidad",
          },
          { value: "week", label: "Una semana después" },
          {
            value: "custom_timing",
            label: "Personalizado (especificar abajo)",
          },
        ],
        helperText: "Cuándo se enviará el email después del disparador",
        required: true,
        section: "trigger",
      },
      {
        id: "custom_timing",
        type: "text",
        label: "Tiempo personalizado",
        placeholder:
          'Ej. "3 días después a las 10am", "Cada martes durante 4 semanas"',
        helperText: "Especifica el momento exacto de envío",
        advanced: true,
        section: "trigger",
      },
      {
        id: "user_context",
        type: "textarea",
        label: "Contexto del usuario",
        placeholder:
          'Ej. "Nuevo suscriptor que descargó guía de SEO", "Cliente que añadió productos pero no completó la compra"',
        helperText: "Describe la situación del usuario al recibir este email",
        required: true,
        section: "trigger",
      },

      // Sección: Contenido y elementos
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo y tono del mensaje",
        required: true,
        section: "content",
      },
      {
        id: "personalization",
        type: "checkbox",
        label: "Elementos de personalización",
        options: [
          { value: "name", label: "Nombre del destinatario" },
          { value: "company", label: "Nombre de la empresa" },
          {
            value: "recent_activity",
            label: "Actividad reciente (productos vistos, etc.)",
          },
          { value: "purchase_history", label: "Historial de compras" },
          { value: "location", label: "Ubicación geográfica" },
          { value: "custom_fields", label: "Campos de datos personalizados" },
        ],
        helperText: "Datos variables para personalizar el contenido",
        section: "content",
      },
      {
        id: "message_content",
        type: "textarea",
        label: "Contenido principal",
        placeholder: "Resume el mensaje principal que quieres comunicar",
        helperText: "Describe el contenido esencial y puntos clave",
        required: true,
        section: "content",
      },
      {
        id: "visual_elements",
        type: "checkbox",
        label: "Elementos visuales",
        options: [
          { value: "header_image", label: "Imagen de cabecera" },
          { value: "logo", label: "Logo de la empresa" },
          { value: "product_images", label: "Imágenes de producto" },
          { value: "gif", label: "GIF animado" },
          { value: "social_icons", label: "Iconos de redes sociales" },
          { value: "dividers", label: "Separadores de sección" },
          { value: "buttons", label: "Botones de acción" },
        ],
        helperText: "Elementos visuales a incluir en el email",
        section: "content",
      },
      {
        id: "cta",
        type: "text",
        label: "Call-to-Action principal",
        placeholder:
          'Ej. "Completar mi compra", "Ver mi cuenta", "Descargar ahora"',
        helperText: "Acción específica que quieres que realice el usuario",
        required: true,
        section: "content",
      },

      // Sección: Aspectos técnicos
      {
        id: "email_series",
        type: "select",
        label: "Parte de una serie",
        options: [
          { value: "no", label: "No, es un email individual" },
          { value: "first", label: "Primero de una serie" },
          { value: "middle", label: "Intermedio en una serie" },
          { value: "last", label: "Último de una serie" },
        ],
        helperText: "Indica si este email forma parte de una secuencia",
        required: true,
        section: "technical",
      },
      {
        id: "follow_up",
        type: "textarea",
        label: "Seguimiento condicional",
        placeholder:
          'Ej. "Si abre el email pero no hace clic, enviar recordatorio en 2 días", "Si hace clic, asignar etiqueta \'interesado\'"',
        helperText: "Acciones adicionales basadas en la respuesta del usuario",
        section: "technical",
      },
      {
        id: "tracking",
        type: "checkbox",
        label: "Elementos de seguimiento",
        options: [
          { value: "open", label: "Seguimiento de aperturas" },
          { value: "clicks", label: "Seguimiento de clics" },
          { value: "conversion", label: "Seguimiento de conversiones" },
          { value: "utm", label: "Parámetros UTM en enlaces" },
          { value: "ab", label: "Preparar para pruebas A/B" },
        ],
        helperText: "Mecanismos para analizar el rendimiento del email",
        section: "technical",
      },
      {
        id: "systems_integration",
        type: "select",
        label: "Integración con sistemas",
        options: [
          { value: "crm", label: "CRM - Actualizar perfil de contacto" },
          {
            value: "ecommerce",
            label: "Ecommerce - Conectar con carrito/catálogo",
          },
          { value: "analytics", label: "Analytics - Eventos personalizados" },
          { value: "tags", label: "Sistema de etiquetas/segmentación" },
          { value: "none", label: "No requiere integración especial" },
        ],
        helperText: "Sistemas con los que este email debe integrarse",
        section: "technical",
      },
    ],
    agents: [
      {
        id: "automation-expert",
        name: "Javier Autómata",
        avatar: "/agents/automation-expert.png",
        role: "Experto en Automatización",
        description:
          "Especialista en estrategias de automatización y flujos de email marketing",
      },
      {
        id: "conversion-copywriter",
        name: "Carmen Conversión",
        avatar: "/agents/conversion-copywriter.png",
        role: "Copywriter de Conversión",
        description:
          "Experta en redacción persuasiva que genera acciones concretas",
      },
    ],
    tips: [
      "Crea un asunto relevante al contexto específico del usuario",
      "Utiliza previsualización del email (preheader) para complementar el asunto",
      "Personaliza más allá del nombre: referencia acciones o preferencias específicas",
      "Mantén un único objetivo claro para cada email automatizado",
      "Utiliza un lenguaje orientado a beneficios, no solo características",
    ],
    examples: [
      {
        title: "Email de bienvenida post-registro",
        description:
          "Mensaje cálido con próximos pasos y recursos para comenzar",
      },
      {
        title: "Recordatorio de carrito abandonado",
        description:
          "Email persuasivo con productos olvidados e incentivo para completar la compra",
      },
      {
        title: "Solicitud de reseña post-compra",
        description:
          "Mensaje de agradecimiento con solicitud de feedback y valoración del producto",
      },
    ],
  },

  // Categoría: Email y Notificaciones - Serie de Bienvenida
  "welcome-series": {
    id: "welcome-series",
    title: "Serie de Bienvenida",
    description:
      "Crea una secuencia de emails para introducir nuevos usuarios a tu marca o producto",
    icon: Send,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de la serie",
        icon: FileText,
      },
      {
        id: "strategy",
        title: "Estrategia y objetivos",
        description: "Propósito y enfoque de la secuencia",
        icon: Target,
      },
      {
        id: "sequence",
        title: "Estructura de la secuencia",
        description: "Organización y contenido de cada email",
        icon: Layout,
      },
      {
        id: "technical",
        title: "Aspectos técnicos",
        description: "Configuración y métricas",
        icon: Settings,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "series_name",
        type: "text",
        label: "Nombre de la serie",
        placeholder:
          'Ej. "Bienvenida a ProductX", "Primeros pasos con ServicioY"',
        helperText: "Nombre interno para identificar esta secuencia",
        required: true,
        section: "basics",
      },
      {
        id: "welcome_context",
        type: "select",
        label: "Contexto de bienvenida",
        options: [
          {
            value: "new_subscriber",
            label: "Nuevo suscriptor (lista/newsletter)",
          },
          { value: "new_customer", label: "Nuevo cliente (primera compra)" },
          { value: "trial_user", label: "Usuario de prueba gratuita" },
          { value: "new_member", label: "Nuevo miembro (comunidad/programa)" },
          { value: "product_onboarding", label: "Onboarding de producto" },
          { value: "course_welcome", label: "Bienvenida a curso/formación" },
        ],
        helperText: "Situación que activa esta serie de bienvenida",
        required: true,
        section: "basics",
      },
      {
        id: "sender_name",
        type: "text",
        label: "Nombre del remitente",
        placeholder: 'Ej. "Ana de EmpresaX", "Equipo de Soporte"',
        helperText: "Quién aparece como remitente de los emails",
        required: true,
        section: "basics",
      },
      {
        id: "total_emails",
        type: "number",
        label: "Número total de emails",
        placeholder: "Ej. 5",
        helperText: "Cuántos emails compondrán la secuencia completa",
        min: 2,
        max: 12,
        required: true,
        section: "basics",
      },

      // Sección: Estrategia y objetivos
      {
        id: "target_audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Describe a los nuevos usuarios que recibirán esta serie",
        helperText:
          "Define características relevantes de tus nuevos suscriptores/usuarios",
        required: true,
        section: "strategy",
      },
      {
        id: "series_objectives",
        type: "checkbox",
        label: "Objetivos de la serie",
        options: [
          { value: "education", label: "Educar sobre el producto/servicio" },
          { value: "engagement", label: "Generar engagement inicial" },
          { value: "purchase", label: "Impulsar primera compra" },
          { value: "usage", label: "Fomentar uso del producto" },
          { value: "brand", label: "Construir relación con la marca" },
          { value: "upsell", label: "Presentar ofertas complementarias" },
          { value: "community", label: "Integrar en la comunidad" },
        ],
        helperText: "Qué buscas lograr con esta serie de bienvenida",
        required: true,
        section: "strategy",
      },
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo y voz a mantener en toda la serie",
        required: true,
        section: "strategy",
      },
      {
        id: "value_proposition",
        type: "textarea",
        label: "Propuesta de valor central",
        placeholder: "Resume en 1-2 frases el valor principal que ofreces",
        helperText: "Beneficio fundamental que debe quedar claro en la serie",
        required: true,
        section: "strategy",
      },

      // Sección: Estructura de la secuencia
      {
        id: "email1_purpose",
        type: "select",
        label: "Propósito del Email 1",
        options: [
          { value: "welcome", label: "Bienvenida y presentación" },
          { value: "expectations", label: "Establecer expectativas" },
          { value: "first_steps", label: "Primeros pasos/Guía inicial" },
          { value: "story", label: "Historia de la marca/misión" },
          { value: "confirmation", label: "Confirmación y agradecimiento" },
        ],
        helperText: "Objetivo principal del primer email",
        required: true,
        section: "sequence",
      },
      {
        id: "email1_content",
        type: "textarea",
        label: "Contenido del Email 1",
        placeholder:
          "Describe los elementos y mensajes principales del primer email",
        helperText: "Detalla el contenido específico del email inicial",
        required: true,
        section: "sequence",
      },
      {
        id: "subsequent_emails",
        type: "textarea",
        label: "Estructura de emails posteriores",
        placeholder:
          "Describe el propósito y contenido de cada email siguiente (Email 2, 3, etc.)",
        helperText: "Detalla la progresión lógica entre emails de la serie",
        required: true,
        section: "sequence",
      },
      {
        id: "cta_progression",
        type: "textarea",
        label: "Progresión de CTAs",
        placeholder:
          'Ej. "Email 1: Completar perfil, Email 2: Ver tutorial, Email 3: Probar función X"',
        helperText: "Secuencia de llamadas a la acción a lo largo de la serie",
        required: true,
        section: "sequence",
      },
      {
        id: "content_elements",
        type: "checkbox",
        label: "Elementos a incluir en la serie",
        options: [
          { value: "tutorials", label: "Tutoriales/Guías" },
          { value: "videos", label: "Videos explicativos" },
          { value: "testimonials", label: "Testimonios de clientes" },
          { value: "faq", label: "Preguntas frecuentes" },
          { value: "tips", label: "Consejos y mejores prácticas" },
          { value: "resources", label: "Recursos descargables" },
          { value: "case_studies", label: "Casos de estudio" },
          { value: "special_offer", label: "Oferta especial/Descuento" },
        ],
        helperText: "Contenidos a distribuir a lo largo de la serie",
        section: "sequence",
      },

      // Sección: Aspectos técnicos
      {
        id: "timing_schedule",
        type: "select",
        label: "Cadencia de envío",
        options: [
          { value: "daily", label: "Diaria (1 email por día)" },
          { value: "every_other", label: "Días alternos (cada 2 días)" },
          { value: "twice_week", label: "Dos veces por semana" },
          { value: "weekly", label: "Semanal (1 email por semana)" },
          { value: "custom", label: "Personalizada (especificar abajo)" },
        ],
        helperText: "Frecuencia de envío entre emails de la serie",
        required: true,
        section: "technical",
      },
      {
        id: "custom_timing",
        type: "textarea",
        label: "Programación personalizada",
        placeholder:
          'Ej. "Email 1: Inmediato, Email 2: +2 días, Email 3: +4 días, Email 4: +7 días"',
        helperText: "Detalla la programación específica entre emails",
        advanced: true,
        section: "technical",
      },
      {
        id: "segmentation",
        type: "select",
        label: "Segmentación durante la serie",
        options: [
          { value: "none", label: "Sin segmentación - Misma serie para todos" },
          {
            value: "behavioral",
            label: "Basada en comportamiento (aperturas/clics)",
          },
          { value: "interest", label: "Basada en intereses demostrados" },
          { value: "activity", label: "Basada en actividad en producto/web" },
          { value: "custom", label: "Personalizada (especificar abajo)" },
        ],
        helperText: "Estrategia de personalización durante la secuencia",
        section: "technical",
      },
      {
        id: "custom_segmentation",
        type: "textarea",
        label: "Estrategia de segmentación personalizada",
        placeholder:
          "Describe cómo se segmentará la audiencia durante la serie",
        helperText: "Detalla criterios y aplicación de la segmentación",
        advanced: true,
        section: "technical",
      },
      {
        id: "success_metrics",
        type: "checkbox",
        label: "Métricas de éxito",
        options: [
          { value: "open_rate", label: "Tasa de apertura" },
          { value: "click_rate", label: "Tasa de clics" },
          { value: "conversion_rate", label: "Tasa de conversión" },
          {
            value: "completion_rate",
            label: "Tasa de finalización de la serie",
          },
          { value: "engagement", label: "Métricas de engagement con producto" },
          { value: "sales", label: "Ventas generadas" },
          { value: "unsubscribe", label: "Tasa de bajas (inversa)" },
        ],
        helperText: "Indicadores para medir el éxito de la serie",
        section: "technical",
      },
    ],
    agents: [
      {
        id: "onboarding-specialist",
        name: "Laura Onboarding",
        avatar: "/agents/onboarding-specialist.png",
        role: "Especialista en Onboarding",
        description:
          "Experta en crear experiencias iniciales efectivas para nuevos usuarios",
      },
      {
        id: "email-strategist",
        name: "Ricardo Estrategia",
        avatar: "/agents/email-strategist.png",
        role: "Estratega de Email Marketing",
        description:
          "Especialista en diseñar secuencias de emails con alto índice de conversión",
      },
    ],
    tips: [
      "Comienza con un email de bienvenida memorable que establezca expectativas claras",
      "Mantén un equilibrio entre educación, valor y promoción a lo largo de la serie",
      "Personaliza contenido basado en información disponible (origen, intereses, etc.)",
      "Cada email debe tener un único objetivo claro y una acción específica",
      "Considera terminar la serie con una solicitud de feedback o siguiente paso concreto",
    ],
    examples: [
      {
        title: "Serie de bienvenida para SaaS",
        description:
          "Secuencia de 5 emails para ayudar a nuevos usuarios a configurar y usar la plataforma",
      },
      {
        title: "Onboarding para ecommerce",
        description:
          "Serie que presenta categorías de productos, beneficios y culmina con primera oferta",
      },
      {
        title: "Bienvenida a comunidad de aprendizaje",
        description:
          "Secuencia que introduce recursos, miembros destacados y fomenta la participación",
      },
    ],
  },

  // Categoría: Web - Página de Destino (Landing Page)
  "landing-page": {
    id: "landing-page",
    title: "Página de Destino",
    description:
      "Crea una landing page enfocada en conversión para promocionar ofertas o servicios",
    icon: Globe,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de la página",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y oferta",
        description: "A quién va dirigida y qué propuesta presenta",
        icon: Target,
      },
      {
        id: "structure",
        title: "Estructura y contenido",
        description: "Secciones y elementos que compondrán la página",
        icon: Layout,
      },
      {
        id: "conversion",
        title: "Conversión y analítica",
        description: "Elementos para maximizar y medir resultados",
        icon: BarChart3,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "page_name",
        type: "text",
        label: "Nombre de la página",
        placeholder: 'Ej. "Lanzamiento ProductX 2025", "Webinar gratuito SEO"',
        helperText: "Nombre interno para identificar esta landing page",
        required: true,
        section: "basics",
      },
      {
        id: "main_headline",
        type: "text",
        label: "Titular principal",
        placeholder:
          'Ej. "Aumenta tus ventas un 200% con nuestra solución de email marketing"',
        helperText: "El mensaje principal que verán los visitantes (H1)",
        required: true,
        section: "basics",
      },
      {
        id: "page_purpose",
        type: "select",
        label: "Propósito principal",
        options: [
          { value: "lead_gen", label: "Generación de leads" },
          { value: "sales", label: "Venta directa" },
          { value: "event", label: "Registro a evento/webinar" },
          { value: "download", label: "Descarga de recurso" },
          { value: "demo", label: "Solicitud de demostración" },
          { value: "trial", label: "Registro a prueba gratuita" },
          { value: "waitlist", label: "Inscripción a lista de espera" },
          { value: "pre_launch", label: "Pre-lanzamiento de producto" },
        ],
        helperText: "Objetivo principal de esta landing page",
        required: true,
        section: "basics",
      },
      {
        id: "campaign_source",
        type: "select",
        label: "Fuente de tráfico principal",
        options: [
          { value: "email", label: "Campaña de email" },
          { value: "social", label: "Redes sociales" },
          { value: "ads", label: "Anuncios pagados" },
          { value: "organic", label: "Búsqueda orgánica" },
          { value: "referral", label: "Referencias/Afiliados" },
          { value: "direct", label: "Tráfico directo" },
          { value: "mixed", label: "Fuentes diversas" },
        ],
        helperText: "De dónde vendrán principalmente los visitantes",
        required: true,
        section: "basics",
      },

      // Sección: Audiencia y oferta
      {
        id: "target_audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién va dirigida esta landing page",
        helperText:
          "Define demografía, intereses y necesidades del visitante ideal",
        required: true,
        section: "audience",
      },
      {
        id: "pain_points",
        type: "textarea",
        label: "Puntos de dolor",
        placeholder:
          "Enumera los problemas o desafíos principales de tu audiencia",
        helperText: "Problemas específicos que tu oferta resuelve",
        required: true,
        section: "audience",
      },
      {
        id: "value_proposition",
        type: "textarea",
        label: "Propuesta de valor",
        placeholder: "Resume claramente qué ofreces y por qué es valioso",
        helperText: "Beneficio principal y diferenciador de tu oferta",
        required: true,
        section: "audience",
      },
      {
        id: "offer_details",
        type: "textarea",
        label: "Detalles de la oferta",
        placeholder:
          "Describe específicamente qué estás ofreciendo, incluyendo precios, duración, etc.",
        helperText: "Información concreta sobre lo que obtendrá el usuario",
        required: true,
        section: "audience",
      },
      {
        id: "offer_urgency",
        type: "select",
        label: "Elementos de urgencia/escasez",
        options: [
          {
            value: "time_limit",
            label: "Límite de tiempo (oferta por tiempo limitado)",
          },
          {
            value: "quantity_limit",
            label: "Cantidad limitada (plazas, productos, etc.)",
          },
          { value: "bonus", label: "Bonos por tiempo limitado" },
          {
            value: "early_bird",
            label: "Descuento/beneficio por acción temprana",
          },
          { value: "none", label: "Sin elementos de urgencia" },
        ],
        helperText: "Factores que motivan a actuar rápidamente",
        required: true,
        section: "audience",
      },

      // Sección: Estructura y contenido
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo comunicativo de la página",
        required: true,
        section: "structure",
      },
      {
        id: "page_sections",
        type: "checkbox",
        label: "Secciones a incluir",
        options: [
          { value: "hero", label: "Hero (titular principal e introducción)" },
          { value: "benefits", label: "Beneficios clave" },
          { value: "features", label: "Características del producto/servicio" },
          { value: "how_it_works", label: "Cómo funciona / Proceso" },
          { value: "testimonials", label: "Testimonios / Social proof" },
          { value: "faq", label: "Preguntas frecuentes" },
          { value: "about", label: "Sobre nosotros / Credibilidad" },
          { value: "pricing", label: "Precios / Planes" },
          {
            value: "comparison",
            label: "Comparativa (vs. competencia o solución actual)",
          },
          { value: "guarantee", label: "Garantía / Política de devolución" },
          { value: "bonuses", label: "Bonos o extras incluidos" },
        ],
        helperText: "Secciones principales que compondrán la landing page",
        required: true,
        section: "structure",
      },
      {
        id: "visual_elements",
        type: "checkbox",
        label: "Elementos visuales",
        options: [
          { value: "hero_image", label: "Imagen principal (hero)" },
          { value: "product_images", label: "Imágenes del producto" },
          { value: "screenshots", label: "Capturas de pantalla" },
          { value: "icons", label: "Iconos para características/beneficios" },
          { value: "illustrations", label: "Ilustraciones/Infografías" },
          { value: "video", label: "Video explicativo/demostrativo" },
          {
            value: "testimonial_photos",
            label: "Fotos de clientes/testimonios",
          },
          { value: "logos", label: "Logos de clientes/prensa/partners" },
        ],
        helperText: "Elementos visuales para reforzar el mensaje",
        section: "structure",
      },
      {
        id: "form_fields",
        type: "checkbox",
        label: "Campos de formulario",
        options: [
          { value: "name", label: "Nombre" },
          { value: "email", label: "Email" },
          { value: "phone", label: "Teléfono" },
          { value: "company", label: "Empresa" },
          { value: "job_title", label: "Cargo" },
          { value: "industry", label: "Industria/Sector" },
          { value: "company_size", label: "Tamaño de empresa" },
          { value: "budget", label: "Presupuesto" },
          { value: "needs", label: "Necesidades/Intereses" },
          {
            value: "custom",
            label: "Campos personalizados (especificar abajo)",
          },
        ],
        helperText: "Información a recopilar en el formulario",
        section: "structure",
      },
      {
        id: "custom_fields",
        type: "textarea",
        label: "Campos personalizados",
        placeholder:
          "Describe campos adicionales específicos para tu formulario",
        helperText:
          'Completa solo si seleccionaste "Campos personalizados" arriba',
        advanced: true,
        section: "structure",
      },

      // Sección: Conversión y analítica
      {
        id: "cta_primary",
        type: "text",
        label: "CTA principal",
        placeholder:
          'Ej. "Obtén tu acceso gratuito", "Regístrate ahora", "Reserva tu plaza"',
        helperText: "Texto del botón de llamada a la acción principal",
        required: true,
        section: "conversion",
      },
      {
        id: "cta_secondary",
        type: "text",
        label: "CTA secundario",
        placeholder: 'Ej. "Ver cómo funciona", "Más información"',
        helperText: "Texto para llamada a la acción alternativa (si aplica)",
        section: "conversion",
      },
      {
        id: "trust_elements",
        type: "checkbox",
        label: "Elementos de confianza",
        options: [
          { value: "testimonials", label: "Testimonios de clientes" },
          { value: "case_studies", label: "Casos de éxito/Resultados" },
          { value: "reviews", label: "Reseñas/Calificaciones" },
          { value: "certifications", label: "Certificaciones/Premios" },
          { value: "security", label: "Sellos de seguridad/privacidad" },
          { value: "guarantees", label: "Garantía de satisfacción/Devolución" },
          { value: "social_proof", label: "Números/Estadísticas de usuarios" },
          { value: "media", label: "Menciones en medios" },
        ],
        helperText: "Elementos para generar confianza y credibilidad",
        required: true,
        section: "conversion",
      },
      {
        id: "tracking_needs",
        type: "checkbox",
        label: "Necesidades de seguimiento",
        options: [
          { value: "pageviews", label: "Vistas de página" },
          { value: "scroll_depth", label: "Profundidad de scroll" },
          { value: "cta_clicks", label: "Clics en CTAs" },
          { value: "form_starts", label: "Inicios de formulario" },
          { value: "conversions", label: "Conversiones completas" },
          { value: "video_plays", label: "Reproducciones de video" },
          { value: "exit_intent", label: "Intención de salida" },
          { value: "heatmap", label: "Mapas de calor" },
        ],
        helperText: "Métricas y comportamientos a monitorizar",
        section: "conversion",
      },
      {
        id: "post_conversion",
        type: "select",
        label: "Experiencia post-conversión",
        options: [
          { value: "thank_you", label: "Página de agradecimiento" },
          { value: "download", label: "Descarga inmediata" },
          { value: "redirect", label: "Redirección a otra página" },
          { value: "video", label: "Video de bienvenida/instrucciones" },
          { value: "upsell", label: "Oferta adicional (upsell)" },
          { value: "welcome_email", label: "Email de bienvenida automático" },
        ],
        helperText: "Qué ocurre inmediatamente después de la conversión",
        required: true,
        section: "conversion",
      },
    ],
    agents: [
      {
        id: "conversion-expert",
        name: "Gabriel Conversión",
        avatar: "/agents/conversion-expert.png",
        role: "Experto en Conversión",
        description:
          "Especialista en optimización de landing pages y técnicas de persuasión",
      },
      {
        id: "copywriter",
        name: "Valeria Copy",
        avatar: "/agents/copywriter.png",
        role: "Copywriter Persuasivo",
        description:
          "Experta en redacción persuasiva enfocada en responder objeciones y motivar a la acción",
      },
    ],
    tips: [
      "Mantén un único objetivo claro y enfocado en toda la página",
      "Usa imágenes relevantes que refuercen el mensaje y muestren el producto en acción",
      "Elimina la navegación principal para reducir distracciones",
      "Incluye múltiples CTAs a lo largo de la página (especialmente después de beneficios clave)",
      "Destaca testimonios reales y específicos que refuercen los beneficios principales",
    ],
    examples: [
      {
        title: "Landing de generación de leads",
        description:
          "Página para descarga de ebook con formulario corto y seguimiento por email",
      },
      {
        title: "Página de registro a webinar",
        description:
          "Landing con contador regresivo, agenda detallada y perfiles de ponentes",
      },
      {
        title: "Landing de venta de curso online",
        description:
          "Página extensa con testimonios en video, desglose de módulos y garantía de satisfacción",
      },
    ],
  },

  // Categoría: Web - Página "Sobre Nosotros"
  "about-us": {
    id: "about-us",
    title: 'Página "Sobre Nosotros"',
    description:
      "Crea una página que comunique la historia, misión y valores de tu marca o empresa",
    icon: Users,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de la empresa",
        icon: Building,
      },
      {
        id: "story",
        title: "Historia y misión",
        description: "Origen y propósito de la empresa",
        icon: BookOpen,
      },
      {
        id: "team",
        title: "Equipo y cultura",
        description: "Personas y ambiente de trabajo",
        icon: Users,
      },
      {
        id: "structure",
        title: "Estructura y diseño",
        description: "Organización y elementos visuales",
        icon: Layout,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "company_name",
        type: "text",
        label: "Nombre de la empresa",
        placeholder: 'Ej. "TechSolutions", "EcoVida Orgánica"',
        helperText: "Nombre completo y oficial de la empresa/marca",
        required: true,
        section: "basics",
      },
      {
        id: "industry",
        type: "select",
        label: "Industria/Sector",
        options: [
          { value: "tech", label: "Tecnología/Software" },
          { value: "ecommerce", label: "Comercio electrónico" },
          { value: "health", label: "Salud/Bienestar" },
          { value: "finance", label: "Finanzas/Seguros" },
          { value: "education", label: "Educación/E-learning" },
          { value: "food", label: "Alimentación/Restauración" },
          { value: "fashion", label: "Moda/Belleza" },
          { value: "travel", label: "Viajes/Turismo" },
          { value: "construction", label: "Construcción/Inmobiliaria" },
          { value: "manufacturing", label: "Fabricación/Industria" },
          { value: "media", label: "Medios/Entretenimiento" },
          { value: "nonprofit", label: "ONG/Sin fines de lucro" },
          { value: "consulting", label: "Consultoría/Servicios profesionales" },
          { value: "other", label: "Otro (especificar abajo)" },
        ],
        helperText: "Sector principal en el que opera la empresa",
        required: true,
        section: "basics",
      },
      {
        id: "custom_industry",
        type: "text",
        label: "Industria personalizada",
        placeholder: 'Especifica tu sector si seleccionaste "Otro"',
        helperText: 'Completa solo si elegiste "Otro" en la pregunta anterior',
        advanced: true,
        section: "basics",
      },
      {
        id: "founding_year",
        type: "text",
        label: "Año de fundación",
        placeholder: 'Ej. "2015", "Fundada en 2008"',
        helperText: "Cuándo se estableció la empresa",
        required: true,
        section: "basics",
      },
      {
        id: "location",
        type: "text",
        label: "Ubicación principal",
        placeholder: 'Ej. "Madrid, España", "Con sedes en México y Colombia"',
        helperText: "Dónde está situada la empresa (sede principal)",
        required: true,
        section: "basics",
      },
      {
        id: "company_size",
        type: "select",
        label: "Tamaño de la empresa",
        options: [
          { value: "micro", label: "Microempresa (1-10 empleados)" },
          { value: "small", label: "Pequeña empresa (11-50 empleados)" },
          { value: "medium", label: "Mediana empresa (51-250 empleados)" },
          { value: "large", label: "Gran empresa (251-1000 empleados)" },
          { value: "enterprise", label: "Corporación (más de 1000 empleados)" },
        ],
        helperText: "Dimensión aproximada de la organización",
        required: true,
        section: "basics",
      },

      // Sección: Historia y misión
      {
        id: "origin_story",
        type: "textarea",
        label: "Historia de origen",
        placeholder: "Narra brevemente cómo y por qué se fundó la empresa",
        helperText:
          "El relato de cómo comenzó todo (problemática inicial, fundadores, etc.)",
        required: true,
        section: "story",
      },
      {
        id: "mission",
        type: "textarea",
        label: "Misión",
        placeholder:
          'Ej. "Nuestra misión es proporcionar soluciones innovadoras que mejoren la calidad de vida de nuestros clientes"',
        helperText:
          "Propósito fundamental de la empresa (qué hace y para quién)",
        required: true,
        section: "story",
      },
      {
        id: "vision",
        type: "textarea",
        label: "Visión",
        placeholder: 'Ej. "Aspiramos a convertirnos en líderes globales en..."',
        helperText: "Aspiración futura y a largo plazo de la empresa",
        section: "story",
      },
      {
        id: "values",
        type: "textarea",
        label: "Valores fundamentales",
        placeholder:
          "Enumera los principios y valores clave que guían a la empresa",
        helperText: "Principios éticos y culturales que definen la identidad",
        required: true,
        section: "story",
      },
      {
        id: "milestones",
        type: "textarea",
        label: "Hitos importantes",
        placeholder: "Enumera eventos clave en la trayectoria de la empresa",
        helperText: "Momentos significativos en la historia de la empresa",
        section: "story",
      },

      // Sección: Equipo y cultura
      {
        id: "leadership",
        type: "textarea",
        label: "Equipo directivo",
        placeholder:
          "Nombra y describe brevemente a los miembros clave del equipo directivo",
        helperText: "Información sobre fundadores y líderes principales",
        section: "team",
      },
      {
        id: "team_focus",
        type: "select",
        label: "Enfoque del equipo",
        options: [
          { value: "leadership", label: "Solo liderazgo/dirección" },
          { value: "departments", label: "Equipos/departamentos clave" },
          { value: "all_team", label: "Todo el equipo" },
          { value: "selective", label: "Miembros seleccionados" },
          { value: "none", label: "Sin presentación de equipo" },
        ],
        helperText: "A quién destacar en la sección de equipo",
        required: true,
        section: "team",
      },
      {
        id: "company_culture",
        type: "textarea",
        label: "Cultura empresarial",
        placeholder:
          "Describe el ambiente de trabajo y la filosofía de la empresa",
        helperText: "Cómo es trabajar en la empresa, qué la hace única",
        section: "team",
      },
      {
        id: "benefits",
        type: "checkbox",
        label: "Beneficios para empleados",
        options: [
          { value: "remote", label: "Trabajo remoto/flexible" },
          { value: "learning", label: "Desarrollo profesional/formación" },
          { value: "health", label: "Seguro médico/beneficios de salud" },
          { value: "vacation", label: "Política generosa de vacaciones" },
          { value: "parental", label: "Permisos parentales" },
          { value: "wellness", label: "Programas de bienestar" },
          { value: "food", label: "Alimentación/comidas" },
          { value: "equity", label: "Participación en la empresa" },
        ],
        helperText:
          "Ventajas de trabajar en la empresa (opcional, si quieres incluir sección de carreras)",
        section: "team",
      },
      {
        id: "social_commitment",
        type: "textarea",
        label: "Compromiso social/ambiental",
        placeholder:
          "Describe iniciativas de responsabilidad corporativa o impacto social",
        helperText:
          "Cómo contribuye la empresa a la sociedad y el medio ambiente",
        section: "team",
      },

      // Sección: Estructura y diseño
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo comunicativo de la página",
        required: true,
        section: "structure",
      },
      {
        id: "sections_include",
        type: "checkbox",
        label: "Secciones a incluir",
        options: [
          { value: "intro", label: "Introducción/Resumen" },
          { value: "story", label: "Historia de la empresa" },
          { value: "mission", label: "Misión, visión y valores" },
          { value: "leadership", label: "Equipo directivo" },
          { value: "team", label: "Equipo completo/departamentos" },
          { value: "testimonials", label: "Testimonios (clientes/empleados)" },
          { value: "achievements", label: "Logros y reconocimientos" },
          { value: "partner", label: "Socios/alianzas" },
          { value: "csr", label: "Responsabilidad social corporativa" },
          { value: "careers", label: "Oportunidades laborales" },
          { value: "contact", label: "Información de contacto" },
        ],
        helperText: 'Apartados que compondrán la página "Sobre Nosotros"',
        required: true,
        section: "structure",
      },
      {
        id: "visual_elements",
        type: "checkbox",
        label: "Elementos visuales",
        options: [
          { value: "team_photos", label: "Fotografías del equipo" },
          {
            value: "office_photos",
            label: "Imágenes de oficinas/instalaciones",
          },
          { value: "timeline", label: "Línea de tiempo/cronología" },
          { value: "infographics", label: "Infografías de datos/logros" },
          { value: "video", label: "Video corporativo" },
          { value: "values_icons", label: "Iconos para valores" },
          { value: "testimonial_carousel", label: "Carrusel de testimonios" },
          { value: "awards", label: "Reconocimientos/premios" },
          { value: "clients", label: "Logos de clientes importantes" },
        ],
        helperText: "Elementos visuales para enriquecer la página",
        section: "structure",
      },
      {
        id: "cta",
        type: "text",
        label: "Call-to-Action principal",
        placeholder:
          'Ej. "Contáctanos", "Únete a nuestro equipo", "Descubre nuestros productos"',
        helperText: "Acción principal que quieres que realicen los visitantes",
        required: true,
        section: "structure",
      },
      {
        id: "page_integration",
        type: "checkbox",
        label: "Integración con otras páginas",
        options: [
          { value: "contact", label: "Página de contacto" },
          { value: "careers", label: "Página de carreras/empleo" },
          { value: "products", label: "Catálogo de productos/servicios" },
          { value: "blog", label: "Blog corporativo" },
          { value: "csr", label: "Página de responsabilidad social" },
          { value: "press", label: "Sala de prensa/medios" },
        ],
        helperText: "Enlaces a otras secciones relevantes del sitio",
        section: "structure",
      },
    ],
    agents: [
      {
        id: "brand-storyteller",
        name: "Elena Narrativa",
        avatar: "/agents/brand-storyteller.png",
        role: "Narradora de Marca",
        description:
          "Especialista en construir historias corporativas auténticas y memorables",
      },
      {
        id: "corporate-communications",
        name: "Carlos Corporativo",
        avatar: "/agents/corporate-communications.png",
        role: "Experto en Comunicación Corporativa",
        description:
          "Especialista en transmitir valores e identidad de marca de forma efectiva",
      },
    ],
    tips: [
      "Incluye una narrativa auténtica que humanice tu marca, no solo datos y fechas",
      "Usa fotografías reales de tu equipo y espacios, evitando imágenes de stock",
      "Incorpora testimonios de clientes o empleados para añadir credibilidad",
      "Comunica claramente tu propuesta de valor y lo que te diferencia",
      "Mantén el contenido actualizado, especialmente cifras, logros y miembros del equipo",
    ],
    examples: [
      {
        title: 'Página "Sobre Nosotros" de startup tecnológica',
        description:
          "Enfoque en la misión innovadora y el equipo diverso de fundadores y desarrolladores",
      },
      {
        title: "Sobre nosotros de marca sostenible",
        description:
          "Énfasis en valores ecológicos, proceso de fabricación y certificaciones",
      },
      {
        title: "Página corporativa de agencia creativa",
        description:
          "Showcase de cultura de trabajo, personalidades del equipo y proyectos destacados",
      },
    ],
  },

  // Categoría: Web - Redacción Web
  "web-copy": {
    id: "web-copy",
    title: "Redacción Web",
    description:
      "Crea contenido persuasivo para páginas web que convierte visitantes en clientes",
    icon: MonitorSmartphone,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de la página",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y propuesta",
        description: "A quién va dirigido y qué ofreces",
        icon: Target,
      },
      {
        id: "structure",
        title: "Estructura y contenido",
        description: "Organización y elementos del copy",
        icon: Layout,
      },
      {
        id: "conversion",
        title: "Conversión y SEO",
        description: "Elementos para optimizar resultados",
        icon: BarChart3,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "page_type",
        type: "select",
        label: "Tipo de página",
        options: [
          { value: "home", label: "Página de inicio" },
          { value: "product", label: "Página de producto" },
          { value: "service", label: "Página de servicio" },
          { value: "features", label: "Página de características" },
          { value: "pricing", label: "Página de precios" },
          { value: "contact", label: "Página de contacto" },
          { value: "faq", label: "Preguntas frecuentes" },
          { value: "category", label: "Página de categoría (tienda)" },
          { value: "other", label: "Otra (especificar abajo)" },
        ],
        helperText: "Qué tipo de página web necesitas redactar",
        required: true,
        section: "basics",
      },
      {
        id: "custom_page_type",
        type: "text",
        label: "Tipo de página personalizado",
        placeholder: 'Especifica el tipo de página si seleccionaste "Otra"',
        helperText: 'Completa solo si elegiste "Otra" en la pregunta anterior',
        advanced: true,
        section: "basics",
      },
      {
        id: "company_name",
        type: "text",
        label: "Nombre de la empresa/marca",
        placeholder: 'Ej. "TechSolutions", "Moda Elegante"',
        helperText: "Nombre de la empresa para la que se crea el contenido",
        required: true,
        section: "basics",
      },
      {
        id: "website_description",
        type: "textarea",
        label: "Descripción del sitio web",
        placeholder:
          "Explica brevemente el propósito general del sitio y cómo encaja esta página",
        helperText: "Contexto sobre el sitio web completo",
        section: "basics",
      },
      {
        id: "website_goals",
        type: "checkbox",
        label: "Objetivos principales del sitio",
        options: [
          { value: "generate_leads", label: "Generar leads/contactos" },
          { value: "sales", label: "Vender productos/servicios" },
          { value: "educate", label: "Educar/informar a la audiencia" },
          { value: "build_community", label: "Construir comunidad" },
          { value: "brand_awareness", label: "Aumentar conocimiento de marca" },
          { value: "support", label: "Proveer soporte/atención" },
        ],
        helperText: "Qué buscas lograr principalmente con el sitio",
        required: true,
        section: "basics",
      },

      // Sección: Audiencia y propuesta
      {
        id: "target_audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién va dirigido este contenido",
        helperText:
          "Define demografía, intereses y necesidades de los visitantes ideales",
        required: true,
        section: "audience",
      },
      {
        id: "pain_points",
        type: "textarea",
        label: "Puntos de dolor",
        placeholder:
          "Enumera los problemas o desafíos principales de tu audiencia",
        helperText: "Problemas específicos que tu producto/servicio resuelve",
        required: true,
        section: "audience",
      },
      {
        id: "unique_value",
        type: "textarea",
        label: "Propuesta de valor única",
        placeholder:
          "Resume claramente qué ofreces y por qué es valioso/diferente",
        helperText: "Lo que hace especial a tu oferta frente a la competencia",
        required: true,
        section: "audience",
      },
      {
        id: "benefits",
        type: "textarea",
        label: "Beneficios principales",
        placeholder:
          "Enumera los beneficios más importantes que obtiene el usuario",
        helperText:
          "Ventajas concretas que recibirá el cliente (no características)",
        required: true,
        section: "audience",
      },
      {
        id: "objections",
        type: "textarea",
        label: "Objeciones a superar",
        placeholder:
          "Enumera las dudas o preocupaciones típicas que puede tener tu audiencia",
        helperText: "Resistencias comunes que deben abordarse en el copy",
        section: "audience",
      },

      // Sección: Estructura y contenido
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo comunicativo del contenido",
        required: true,
        section: "structure",
      },
      {
        id: "headline",
        type: "text",
        label: "Titular principal (H1)",
        placeholder:
          'Ej. "Transforma tu productividad con nuestra solución todo-en-uno"',
        helperText: "El mensaje más importante que verán los visitantes",
        required: true,
        section: "structure",
      },
      {
        id: "subheadline",
        type: "text",
        label: "Subtitular (H2)",
        placeholder:
          'Ej. "Ahorra tiempo, reduce estrés y mejora resultados con nuestro software intuitivo"',
        helperText: "Complemento al titular que expande el mensaje principal",
        section: "structure",
      },
      {
        id: "sections_to_include",
        type: "checkbox",
        label: "Secciones a incluir",
        options: [
          { value: "intro", label: "Introducción/Gancho inicial" },
          { value: "problem", label: "Problema/Situación actual" },
          { value: "solution", label: "Solución/Producto" },
          { value: "benefits", label: "Beneficios principales" },
          { value: "features", label: "Características destacadas" },
          { value: "social_proof", label: "Prueba social/Testimonios" },
          { value: "faq", label: "Preguntas frecuentes" },
          { value: "cta", label: "Llamada a la acción" },
        ],
        helperText: "Apartados que compondrán la estructura del copy",
        required: true,
        section: "structure",
      },
      {
        id: "content_elements",
        type: "checkbox",
        label: "Elementos de contenido",
        options: [
          { value: "bullet_points", label: "Listas con viñetas" },
          { value: "quotes", label: "Citas/Testimonios destacados" },
          { value: "statistics", label: "Estadísticas/Datos relevantes" },
          { value: "examples", label: "Ejemplos/Casos prácticos" },
          { value: "comparison", label: "Comparativas (antes/después)" },
          { value: "guarantees", label: "Garantías/Promesas" },
          { value: "storytelling", label: "Elementos narrativos" },
        ],
        helperText: "Recursos que enriquecerán el contenido",
        section: "structure",
      },
      {
        id: "visual_suggestions",
        type: "textarea",
        label: "Sugerencias visuales",
        placeholder:
          "Describe imágenes, iconos u otros elementos visuales que complementarían el texto",
        helperText: "Ideas para acompañar visualmente el contenido",
        section: "structure",
      },

      // Sección: Conversión y SEO
      {
        id: "primary_cta",
        type: "text",
        label: "CTA principal",
        placeholder:
          'Ej. "Comienza tu prueba gratuita", "Solicita una demostración"',
        helperText: "Texto del botón de llamada a la acción principal",
        required: true,
        section: "conversion",
      },
      {
        id: "secondary_cta",
        type: "text",
        label: "CTA secundario",
        placeholder: 'Ej. "Saber más", "Ver planes y precios"',
        helperText: "Llamada a la acción alternativa (si aplica)",
        section: "conversion",
      },
      {
        id: "trust_elements",
        type: "checkbox",
        label: "Elementos de confianza",
        options: [
          { value: "testimonials", label: "Testimonios de clientes" },
          { value: "reviews", label: "Reseñas/Valoraciones" },
          { value: "case_studies", label: "Casos de éxito" },
          { value: "logos", label: "Logos de clientes/partners" },
          { value: "certifications", label: "Certificaciones/Premios" },
          { value: "guarantees", label: "Garantías/Políticas de devolución" },
          { value: "security", label: "Sellos de seguridad/privacidad" },
        ],
        helperText: "Elementos para generar confianza y credibilidad",
        section: "conversion",
      },
      {
        id: "seo_keywords",
        type: "textarea",
        label: "Palabras clave SEO",
        placeholder:
          "Enumera palabras clave principales y secundarias para SEO",
        helperText: "Términos relevantes para posicionamiento en buscadores",
        section: "conversion",
      },
      {
        id: "meta_description",
        type: "textarea",
        label: "Meta descripción",
        placeholder:
          "Escribe una descripción atractiva de 150-160 caracteres para resultados de búsqueda",
        helperText: "Texto que aparecerá en los resultados de búsqueda",
        section: "conversion",
      },
    ],
    agents: [
      {
        id: "web-copywriter",
        name: "Pablo Persuasivo",
        avatar: "/agents/web-copywriter.png",
        role: "Copywriter Web",
        description: "Especialista en copy persuasivo que impulsa a la acción",
      },
      {
        id: "ux-writer",
        name: "Lucía UX",
        avatar: "/agents/ux-writer.png",
        role: "UX Writer",
        description:
          "Experta en experiencia de usuario y jerarquía de información",
      },
    ],
    tips: [
      "Prioriza los beneficios sobre las características técnicas",
      "Usa un lenguaje claro, directo y orientado a la acción",
      "Incluye llamadas a la acción (CTAs) visibles y específicas",
      "Organiza el contenido en secciones con subtítulos descriptivos",
      "Incorpora elementos que refuercen la credibilidad y reduzcan la fricción",
    ],
    examples: [
      {
        title: "Copy para página de inicio SaaS",
        description:
          "Estructura clara con beneficios destacados, testimonios y múltiples CTAs",
      },
      {
        title: "Página de producto ecommerce",
        description:
          "Descripción persuasiva que destaca características, usos y opiniones de clientes",
      },
      {
        title: "Copy para página de servicios profesionales",
        description:
          "Contenido que establece autoridad, muestra proceso y genera confianza",
      },
    ],
  },

  // Categoría: Publicidad - Copy para Anuncios
  "ad-copy": {
    id: "ad-copy",
    title: "Copy para Anuncios",
    description:
      "Crea textos publicitarios efectivos para diferentes plataformas y formatos",
    icon: BadgeDollarSign,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de la campaña",
        icon: FileText,
      },
      {
        id: "platform",
        title: "Plataforma y formato",
        description: "Dónde se publicarán los anuncios",
        icon: Share2,
      },
      {
        id: "offer",
        title: "Propuesta y audiencia",
        description: "Qué ofreces y a quién",
        icon: Target,
      },
      {
        id: "creative",
        title: "Enfoque creativo",
        description: "Estilo y elementos del anuncio",
        icon: Palette,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "campaign_name",
        type: "text",
        label: "Nombre de la campaña",
        placeholder: 'Ej. "Lanzamiento ProductX 2025", "Promoción Verano"',
        helperText: "Identificador interno de la campaña publicitaria",
        required: true,
        section: "basics",
      },
      {
        id: "company_name",
        type: "text",
        label: "Nombre de la empresa/marca",
        placeholder: 'Ej. "TechSolutions", "Moda Elegante"',
        helperText: "Nombre de la marca anunciante",
        required: true,
        section: "basics",
      },
      {
        id: "product_service",
        type: "text",
        label: "Producto o servicio",
        placeholder: 'Ej. "Software de gestión", "Zapatillas deportivas XYZ"',
        helperText: "Qué estás promocionando específicamente",
        required: true,
        section: "basics",
      },
      {
        id: "campaign_objective",
        type: "select",
        label: "Objetivo principal",
        options: [
          { value: "awareness", label: "Conocimiento de marca" },
          { value: "consideration", label: "Consideración de producto" },
          { value: "conversion", label: "Conversión/Ventas" },
          { value: "lead_gen", label: "Generación de leads" },
          { value: "engagement", label: "Engagement/Interacción" },
          { value: "traffic", label: "Tráfico a sitio web" },
          { value: "app_install", label: "Instalación de aplicación" },
          { value: "event", label: "Promoción de evento" },
        ],
        helperText: "Qué buscas lograr con esta campaña",
        required: true,
        section: "basics",
      },
      {
        id: "budget_level",
        type: "select",
        label: "Nivel de presupuesto",
        options: [
          { value: "low", label: "Bajo (test/campaña pequeña)" },
          { value: "medium", label: "Medio (campaña estándar)" },
          { value: "high", label: "Alto (campaña prioritaria)" },
        ],
        helperText: "Indicador relativo de inversión en la campaña",
        section: "basics",
      },

      // Sección: Plataforma y formato
      {
        id: "ad_platform",
        type: "select",
        label: "Plataforma principal",
        options: [
          { value: "google", label: "Google Ads" },
          { value: "facebook", label: "Facebook/Instagram Ads" },
          { value: "linkedin", label: "LinkedIn Ads" },
          { value: "twitter", label: "Twitter/X Ads" },
          { value: "tiktok", label: "TikTok Ads" },
          { value: "pinterest", label: "Pinterest Ads" },
          { value: "youtube", label: "YouTube Ads" },
          { value: "amazon", label: "Amazon Ads" },
          { value: "display", label: "Display/Programática" },
          { value: "other", label: "Otra (especificar abajo)" },
        ],
        helperText: "Dónde se publicarán principalmente los anuncios",
        required: true,
        section: "platform",
      },
      {
        id: "custom_platform",
        type: "text",
        label: "Plataforma personalizada",
        placeholder: 'Especifica la plataforma si seleccionaste "Otra"',
        helperText: 'Completa solo si elegiste "Otra" en la pregunta anterior',
        advanced: true,
        section: "platform",
      },
      {
        id: "ad_format",
        type: "select",
        label: "Formato de anuncio",
        options: [
          { value: "search", label: "Anuncios de búsqueda (texto)" },
          { value: "display", label: "Anuncios display (banner)" },
          { value: "feed", label: "Anuncios en feed (imagen/video)" },
          { value: "stories", label: "Anuncios en Stories" },
          { value: "video", label: "Anuncios de video" },
          { value: "carousel", label: "Anuncios de carrusel" },
          { value: "collection", label: "Anuncios de colección/catálogo" },
          { value: "native", label: "Anuncios nativos" },
          { value: "pla", label: "Anuncios de producto (PLA)" },
          { value: "lead", label: "Anuncios de generación de leads" },
          { value: "other", label: "Otro (especificar abajo)" },
        ],
        helperText: "Tipo específico de anuncio a crear",
        required: true,
        section: "platform",
      },
      {
        id: "custom_format",
        type: "text",
        label: "Formato personalizado",
        placeholder: 'Especifica el formato si seleccionaste "Otro"',
        helperText: 'Completa solo si elegiste "Otro" en la pregunta anterior',
        advanced: true,
        section: "platform",
      },
      {
        id: "character_limits",
        type: "text",
        label: "Limitaciones de caracteres",
        placeholder: 'Ej. "Título: 30 caracteres, Descripción: 90 caracteres"',
        helperText: "Restricciones de longitud según formato/plataforma",
        section: "platform",
      },
      {
        id: "destination",
        type: "text",
        label: "Destino del anuncio",
        placeholder:
          'Ej. "Página de producto", "Landing page promocional", "Formulario de contacto"',
        helperText: "Dónde llevarás al usuario tras hacer clic",
        required: true,
        section: "platform",
      },

      // Sección: Propuesta y audiencia
      {
        id: "target_audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién va dirigido este anuncio",
        helperText: "Define características relevantes del público objetivo",
        required: true,
        section: "offer",
      },
      {
        id: "pain_points",
        type: "textarea",
        label: "Puntos de dolor/necesidades",
        placeholder:
          "Enumera los problemas o necesidades principales de tu audiencia",
        helperText: "Problemas específicos que aborda tu oferta",
        required: true,
        section: "offer",
      },
      {
        id: "unique_value",
        type: "textarea",
        label: "Propuesta de valor única",
        placeholder:
          "Resume claramente qué ofreces y por qué es valioso/diferente",
        helperText: "Lo que te diferencia de la competencia",
        required: true,
        section: "offer",
      },
      {
        id: "offer_details",
        type: "textarea",
        label: "Detalles de la oferta",
        placeholder:
          "Especifica precio, condiciones, plazos, etc. si es relevante",
        helperText: "Información concreta sobre lo que promueves",
        section: "offer",
      },
      {
        id: "urgency_factor",
        type: "select",
        label: "Factor de urgencia",
        options: [
          { value: "none", label: "Sin elemento de urgencia" },
          { value: "limited_time", label: "Tiempo limitado" },
          { value: "limited_quantity", label: "Cantidad limitada" },
          { value: "exclusive", label: "Acceso exclusivo" },
          { value: "deadline", label: "Fecha límite específica" },
          { value: "seasonal", label: "Oferta estacional" },
        ],
        helperText: "Elemento que motiva acción inmediata (si aplica)",
        section: "offer",
      },

      // Sección: Enfoque creativo
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo comunicativo del anuncio",
        required: true,
        section: "creative",
      },
      {
        id: "creative_approach",
        type: "select",
        label: "Enfoque creativo",
        options: [
          { value: "direct", label: "Directo (enfocado en la oferta)" },
          { value: "emotional", label: "Emocional (enfocado en sentimientos)" },
          { value: "problem_solution", label: "Problema-solución" },
          { value: "testimonial", label: "Testimonial/Social proof" },
          {
            value: "comparative",
            label: "Comparativo (vs competencia/alternativa)",
          },
          { value: "educational", label: "Educativo/Informativo" },
          { value: "storytelling", label: "Narrativo (contar una historia)" },
          { value: "humor", label: "Humorístico" },
          { value: "fomo", label: "FOMO (miedo a perderse algo)" },
        ],
        helperText: "Estrategia persuasiva principal",
        required: true,
        section: "creative",
      },
      {
        id: "headlines",
        type: "textarea",
        label: "Ideas para títulos/headlines",
        placeholder:
          "Propón algunas ideas para titulares principales (varios si es posible)",
        helperText: "Propuestas para el mensaje principal del anuncio",
        required: true,
        section: "creative",
      },
      {
        id: "primary_cta",
        type: "text",
        label: "CTA principal",
        placeholder:
          'Ej. "Comprar ahora", "Reserva tu plaza", "Más información"',
        helperText: "Texto del botón o llamada a la acción",
        required: true,
        section: "creative",
      },
      {
        id: "visual_concept",
        type: "textarea",
        label: "Concepto visual",
        placeholder:
          "Describe las imágenes o elementos visuales que acompañarían el copy",
        helperText: "Ideas para los elementos visuales del anuncio",
        section: "creative",
      },
      {
        id: "keywords",
        type: "textarea",
        label: "Palabras clave (para anuncios de búsqueda)",
        placeholder:
          "Enumera palabras clave relevantes para anuncios de búsqueda",
        helperText:
          "Términos de búsqueda para los que el anuncio debería aparecer",
        section: "creative",
      },
    ],
    agents: [
      {
        id: "ad-copywriter",
        name: "Diego Display",
        avatar: "/agents/ad-copywriter.png",
        role: "Copywriter Publicitario",
        description:
          "Especialista en textos publicitarios persuasivos y de alto impacto",
      },
      {
        id: "performance-marketer",
        name: "Marta Métrica",
        avatar: "/agents/performance-marketer.png",
        role: "Experta en Marketing de Performance",
        description:
          "Especialista en optimización de anuncios para máxima conversión",
      },
    ],
    tips: [
      "Mantén tu mensaje claro, directo y centrado en un único objetivo",
      "Destaca beneficios específicos, no solo características",
      "Usa un lenguaje que resuene con tu audiencia específica",
      "Incluye una llamada a la acción clara que indique el siguiente paso",
      "Prueba diferentes variaciones para identificar qué funciona mejor",
    ],
    examples: [
      {
        title: "Anuncio de búsqueda para SaaS",
        description:
          "Copy que destaca solución a problema específico con CTA para prueba gratuita",
      },
      {
        title: "Anuncio de Facebook para ecommerce",
        description:
          "Formato carrusel mostrando productos con descuento y urgencia temporal",
      },
      {
        title: "Anuncio de LinkedIn para B2B",
        description:
          "Enfoque educativo con estadística impactante y oferta de webinar gratuito",
      },
    ],
  },

  // Categoría: Audio - Guion para Podcast
  "podcast-script": {
    id: "podcast-script",
    title: "Guion para Podcast",
    description:
      "Crea un guion estructurado para episodios de podcast profesionales y atractivos",
    icon: Mic2,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales del episodio",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y enfoque",
        description: "A quién va dirigido y con qué enfoque",
        icon: Users,
      },
      {
        id: "structure",
        title: "Estructura y contenido",
        description: "Organización y elementos del episodio",
        icon: LayoutTemplate,
      },
      {
        id: "technical",
        title: "Aspectos técnicos",
        description: "Elementos de producción y distribución",
        icon: Settings,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "podcast_name",
        type: "text",
        label: "Nombre del podcast",
        placeholder:
          'Ej. "Marketing Digital al Día", "Historias de Emprendimiento"',
        helperText: "Título del podcast o programa",
        required: true,
        section: "basics",
      },
      {
        id: "episode_title",
        type: "text",
        label: "Título del episodio",
        placeholder:
          'Ej. "Cómo implementar una estrategia de SEO efectiva", "Entrevista con María García sobre liderazgo"',
        helperText: "Título específico para este episodio",
        required: true,
        section: "basics",
      },
      {
        id: "episode_number",
        type: "text",
        label: "Número/temporada del episodio",
        placeholder: 'Ej. "Episodio 12", "S2E5", "Especial de verano"',
        helperText: "Identificador dentro de la serie (si aplica)",
        section: "basics",
      },
      {
        id: "episode_type",
        type: "select",
        label: "Tipo de episodio",
        options: [
          { value: "solo", label: "Solo/Monólogo (un solo presentador)" },
          { value: "co_hosted", label: "Co-presentado (múltiples hosts)" },
          { value: "interview", label: "Entrevista (host + invitado)" },
          {
            value: "panel",
            label: "Panel/Mesa redonda (varios participantes)",
          },
          { value: "documentary", label: "Documental/Narrativo" },
          { value: "educational", label: "Educativo/Tutorial" },
          { value: "news", label: "Noticias/Actualidad" },
          { value: "case_study", label: "Caso de estudio/Análisis" },
        ],
        helperText: "Formato principal del episodio",
        required: true,
        section: "basics",
      },
      {
        id: "duration_target",
        type: "select",
        label: "Duración objetivo",
        options: [
          { value: "mini", label: "Mini (5-10 minutos)" },
          { value: "short", label: "Corto (10-20 minutos)" },
          { value: "medium", label: "Medio (20-40 minutos)" },
          { value: "long", label: "Largo (40-60 minutos)" },
          { value: "extended", label: "Extendido (más de 60 minutos)" },
        ],
        helperText: "Duración aproximada planeada para el episodio",
        required: true,
        section: "basics",
      },

      // Sección: Audiencia y enfoque
      {
        id: "target_audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién va dirigido este episodio",
        helperText:
          "Define características, intereses y nivel de conocimiento del oyente ideal",
        required: true,
        section: "audience",
      },
      {
        id: "audience_knowledge",
        type: "select",
        label: "Nivel de conocimiento de la audiencia",
        options: [
          {
            value: "beginner",
            label: "Principiante (sin conocimientos previos)",
          },
          {
            value: "intermediate",
            label: "Intermedio (familiarizado con conceptos básicos)",
          },
          {
            value: "advanced",
            label: "Avanzado (conocimiento profundo del tema)",
          },
          {
            value: "mixed",
            label: "Mixto (diferentes niveles de experiencia)",
          },
        ],
        helperText: "Qué tanto sabe tu audiencia sobre el tema",
        required: true,
        section: "audience",
      },
      {
        id: "topic_description",
        type: "textarea",
        label: "Descripción del tema",
        placeholder: "Explica detalladamente el tema principal del episodio",
        helperText: "De qué trata este episodio específicamente",
        required: true,
        section: "audience",
      },
      {
        id: "key_takeaways",
        type: "textarea",
        label: "Principales aprendizajes",
        placeholder:
          "Enumera 3-5 puntos clave que quieres que la audiencia recuerde",
        helperText: "Lo más importante que la audiencia debe llevarse",
        required: true,
        section: "audience",
      },
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo comunicativo del episodio",
        required: true,
        section: "audience",
      },

      // Sección: Estructura y contenido
      {
        id: "participants",
        type: "textarea",
        label: "Participantes",
        placeholder:
          "Enumera presentador(es) e invitados con breve bio relevante",
        helperText: "Quién participa en el episodio y su rol/expertise",
        required: true,
        section: "structure",
      },
      {
        id: "intro_approach",
        type: "textarea",
        label: "Enfoque de introducción",
        placeholder:
          "Describe cómo quieres abrir el episodio (gancho, pregunta, dato sorprendente, etc.)",
        helperText: "Cómo captarás la atención en los primeros minutos",
        required: true,
        section: "structure",
      },
      {
        id: "episode_segments",
        type: "textarea",
        label: "Segmentos/secciones",
        placeholder:
          "Enumera y describe las diferentes partes del episodio en orden",
        helperText: "Estructura por bloques/secciones con tiempos aproximados",
        required: true,
        section: "structure",
      },
      {
        id: "interview_questions",
        type: "textarea",
        label: "Preguntas para entrevista",
        placeholder:
          "Enumera las preguntas principales a realizar al invitado (si aplica)",
        helperText: "Preguntas clave preparadas para la conversación",
        section: "structure",
      },
      {
        id: "outro_approach",
        type: "textarea",
        label: "Enfoque de cierre",
        placeholder:
          "Describe cómo quieres concluir el episodio (resumen, reflexión, CTA, etc.)",
        helperText: "Cómo finalizarás de manera efectiva el episodio",
        required: true,
        section: "structure",
      },

      // Sección: Aspectos técnicos
      {
        id: "production_elements",
        type: "checkbox",
        label: "Elementos de producción",
        options: [
          { value: "intro_music", label: "Música de introducción" },
          { value: "sound_effects", label: "Efectos de sonido" },
          {
            value: "segment_transitions",
            label: "Transiciones entre segmentos",
          },
          { value: "background_music", label: "Música de fondo" },
          { value: "audio_clips", label: "Clips de audio externos" },
          { value: "sound_design", label: "Diseño sonoro especial" },
          { value: "outro_music", label: "Música de cierre" },
        ],
        helperText: "Elementos sonoros a incluir en la producción",
        section: "technical",
      },
      {
        id: "sponsor_mention",
        type: "textarea",
        label: "Menciones de patrocinadores",
        placeholder:
          "Texto específico para mencionar patrocinadores/publicidad (si aplica)",
        helperText: "Script para menciones comerciales",
        section: "technical",
      },
      {
        id: "call_to_action",
        type: "textarea",
        label: "Llamadas a la acción",
        placeholder:
          'Ej. "Suscríbete al podcast", "Visita nuestra web", "Déjanos un comentario"',
        helperText: "Acciones que quieres que realice el oyente",
        required: true,
        section: "technical",
      },
      {
        id: "episode_description",
        type: "textarea",
        label: "Descripción del episodio",
        placeholder:
          "Texto para la descripción en plataformas de podcast (Apple Podcasts, Spotify, etc.)",
        helperText: "Texto promocional que verán potenciales oyentes",
        required: true,
        section: "technical",
      },
      {
        id: "show_notes",
        type: "textarea",
        label: "Elementos para show notes",
        placeholder:
          "Enlaces, recursos mencionados, timestamps importantes, etc.",
        helperText:
          "Información complementaria para incluir en notas del episodio",
        section: "technical",
      },
    ],
    agents: [
      {
        id: "audio-content-producer",
        name: "Amanda Audio",
        avatar: "/agents/audio-content-producer.png",
        role: "Productora de Contenido de Audio",
        description:
          "Especialista en narrativas sonoras y estructura de programas de audio",
      },
      {
        id: "podcast-host",
        name: "Héctor Host",
        avatar: "/agents/podcast-host.png",
        role: "Presentador Experimentado",
        description:
          "Experto en conducir conversaciones fluidas y mantener el interés de la audiencia",
      },
    ],
    tips: [
      "Comienza con un gancho fuerte que capture la atención en los primeros 30 segundos",
      "Mantén un ritmo dinámico alternando segmentos, voces o enfoques",
      "Usa un lenguaje conversacional y natural, evitando sonar demasiado leído",
      'Incluye señalizaciones verbales ("ahora hablaremos sobre...", "como mencionamos antes...") para guiar al oyente',
      "Termina con una conclusión clara y una llamada a la acción específica",
    ],
    examples: [
      {
        title: "Episodio de entrevista con experto",
        description:
          "Estructura basada en preguntas preparadas con espacio para profundizar según las respuestas",
      },
      {
        title: "Episodio educativo paso a paso",
        description:
          "Guion detallado con explicaciones claras de conceptos y ejemplos prácticos",
      },
      {
        title: "Episodio de análisis de tendencias",
        description:
          "Formato de discusión con múltiples perspectivas y datos relevantes",
      },
    ],
  },

  // Categoría: Estrategia - Calendario de Contenidos
  "content-calendar": {
    id: "content-calendar",
    title: "Calendario de Contenidos",
    description:
      "Planifica una estrategia de contenidos organizada para diferentes canales y plataformas",
    icon: CalendarDays,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de la estrategia",
        icon: FileText,
      },
      {
        id: "strategy",
        title: "Enfoque estratégico",
        description: "Objetivos y audiencia",
        icon: Target,
      },
      {
        id: "content",
        title: "Tipos de contenido",
        description: "Formatos y canales",
        icon: LayoutList,
      },
      {
        id: "planning",
        title: "Planificación y recursos",
        description: "Logística y organización",
        icon: Calendar,
      },
    ],
    questions: [
      // Sección: Información básica
      {
        id: "brand_name",
        type: "text",
        label: "Nombre de la marca/empresa",
        placeholder: 'Ej. "TechSolutions", "Moda Elegante"',
        helperText: "Nombre de la empresa para la que se crea el calendario",
        required: true,
        section: "basics",
      },
      {
        id: "industry",
        type: "select",
        label: "Industria/Sector",
        options: [
          { value: "tech", label: "Tecnología/Software" },
          { value: "ecommerce", label: "Comercio electrónico" },
          { value: "health", label: "Salud/Bienestar" },
          { value: "finance", label: "Finanzas/Seguros" },
          { value: "education", label: "Educación/E-learning" },
          { value: "food", label: "Alimentación/Restauración" },
          { value: "fashion", label: "Moda/Belleza" },
          { value: "travel", label: "Viajes/Turismo" },
          { value: "construction", label: "Construcción/Inmobiliaria" },
          { value: "manufacturing", label: "Fabricación/Industria" },
          { value: "media", label: "Medios/Entretenimiento" },
          { value: "nonprofit", label: "ONG/Sin fines de lucro" },
          { value: "consulting", label: "Consultoría/Servicios profesionales" },
          { value: "other", label: "Otro (especificar abajo)" },
        ],
        helperText: "Sector principal en el que opera la empresa",
        required: true,
        section: "basics",
      },
      {
        id: "custom_industry",
        type: "text",
        label: "Industria personalizada",
        placeholder: 'Especifica tu sector si seleccionaste "Otro"',
        helperText: 'Completa solo si elegiste "Otro" en la pregunta anterior',
        advanced: true,
        section: "basics",
      },
      {
        id: "timeframe",
        type: "select",
        label: "Período de planificación",
        options: [
          { value: "month", label: "Mensual" },
          { value: "quarter", label: "Trimestral" },
          { value: "semester", label: "Semestral" },
          { value: "year", label: "Anual" },
        ],
        helperText: "Horizonte temporal del calendario de contenidos",
        required: true,
        section: "basics",
      },
      {
        id: "specific_period",
        type: "text",
        label: "Período específico",
        placeholder: 'Ej. "Enero 2026", "Q2 2025", "Segundo semestre 2025"',
        helperText: "Momento concreto para el que planificas",
        required: true,
        section: "basics",
      },
      {
        id: "team_size",
        type: "select",
        label: "Tamaño del equipo de contenidos",
        options: [
          { value: "solo", label: "Individual (1 persona)" },
          { value: "small", label: "Pequeño (2-3 personas)" },
          { value: "medium", label: "Mediano (4-8 personas)" },
          { value: "large", label: "Grande (más de 8 personas)" },
          { value: "agency", label: "Externo/Agencia" },
        ],
        helperText: "Recursos humanos disponibles para ejecutar la estrategia",
        section: "basics",
      },

      // Sección: Enfoque estratégico
      {
        id: "content_goals",
        type: "checkbox",
        label: "Objetivos principales",
        options: [
          { value: "awareness", label: "Conocimiento de marca" },
          { value: "engagement", label: "Engagement/Interacción" },
          { value: "lead_gen", label: "Generación de leads" },
          { value: "conversion", label: "Conversión/Ventas" },
          { value: "retention", label: "Retención de clientes" },
          { value: "authority", label: "Autoridad/Thought leadership" },
          { value: "seo", label: "Mejora de SEO" },
          { value: "community", label: "Construcción de comunidad" },
          { value: "customer_education", label: "Educación de usuarios" },
        ],
        helperText: "Qué buscas lograr con tu estrategia de contenidos",
        required: true,
        section: "strategy",
      },
      {
        id: "target_audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién va dirigido tu contenido",
        helperText:
          "Define características, intereses y necesidades del público objetivo",
        required: true,
        section: "strategy",
      },
      {
        id: "customer_journey",
        type: "checkbox",
        label: "Etapas del journey a cubrir",
        options: [
          { value: "awareness", label: "Conciencia del problema" },
          { value: "consideration", label: "Consideración de soluciones" },
          { value: "decision", label: "Decisión de compra" },
          { value: "retention", label: "Retención/Fidelización" },
          { value: "advocacy", label: "Recomendación/Advocacy" },
        ],
        helperText: "Fases del recorrido del cliente que abordarás",
        required: true,
        section: "strategy",
      },
      {
        id: "tone",
        type: "tone",
        label: "Tono de comunicación",
        helperText: "Define el estilo comunicativo general",
        required: true,
        section: "strategy",
      },
      {
        id: "key_topics",
        type: "textarea",
        label: "Temas clave",
        placeholder:
          "Enumera los temas principales que quieres abordar en tu estrategia",
        helperText: "Temáticas fundamentales para tu calendario",
        required: true,
        section: "strategy",
      },

      // Sección: Tipos de contenido
      {
        id: "platforms",
        type: "checkbox",
        label: "Plataformas/Canales",
        options: [
          { value: "blog", label: "Blog/Sitio web" },
          { value: "instagram", label: "Instagram" },
          { value: "facebook", label: "Facebook" },
          { value: "linkedin", label: "LinkedIn" },
          { value: "twitter", label: "Twitter/X" },
          { value: "tiktok", label: "TikTok" },
          { value: "youtube", label: "YouTube" },
          { value: "pinterest", label: "Pinterest" },
          { value: "podcast", label: "Podcast" },
          { value: "email", label: "Email marketing" },
          { value: "other", label: "Otro (especificar abajo)" },
        ],
        helperText: "Dónde publicarás tu contenido",
        required: true,
        section: "content",
      },
      {
        id: "custom_platforms",
        type: "text",
        label: "Plataformas personalizadas",
        placeholder: 'Especifica otras plataformas si seleccionaste "Otro"',
        helperText: 'Completa solo si elegiste "Otro" en la pregunta anterior',
        advanced: true,
        section: "content",
      },
      {
        id: "content_formats",
        type: "checkbox",
        label: "Formatos de contenido",
        options: [
          { value: "blog_posts", label: "Artículos de blog" },
          { value: "social_posts", label: "Posts en redes sociales" },
          { value: "videos", label: "Videos" },
          { value: "infographics", label: "Infografías" },
          { value: "ebooks", label: "Ebooks/Guías descargables" },
          { value: "case_studies", label: "Casos de estudio" },
          { value: "podcasts", label: "Episodios de podcast" },
          { value: "webinars", label: "Webinars/Eventos virtuales" },
          { value: "newsletters", label: "Newsletters" },
          { value: "templates", label: "Plantillas/Recursos" },
          { value: "interactive", label: "Contenido interactivo" },
          { value: "ugc", label: "Contenido generado por usuarios" },
        ],
        helperText: "Tipos de contenido que crearás",
        required: true,
        section: "content",
      },
      {
        id: "content_mix",
        type: "select",
        label: "Proporción de contenido",
        options: [
          { value: "rule", label: "Regla 80/20 (80% valor, 20% promoción)" },
          {
            value: "thirds",
            label: "Regla de tercios (⅓ propio, ⅓ curado, ⅓ promocional)",
          },
          { value: "pillar", label: "Contenido pilar + derivados" },
          {
            value: "custom",
            label: "Proporción personalizada (especificar abajo)",
          },
        ],
        helperText: "Estrategia de distribución por tipos de contenido",
        section: "content",
      },
      {
        id: "custom_mix",
        type: "textarea",
        label: "Proporción personalizada",
        placeholder:
          'Describe tu estrategia de mezcla de contenidos si seleccionaste "Personalizada"',
        helperText:
          'Completa solo si elegiste "Proporción personalizada" arriba',
        advanced: true,
        section: "content",
      },
      {
        id: "content_pillars",
        type: "textarea",
        label: "Pilares de contenido",
        placeholder:
          "Enumera los 3-5 pilares temáticos principales de tu estrategia",
        helperText:
          "Categorías temáticas fundamentales que estructuran tu contenido",
        section: "content",
      },

      // Sección: Planificación y recursos
      {
        id: "publishing_frequency",
        type: "textarea",
        label: "Frecuencia de publicación",
        placeholder:
          'Ej. "Blog: 2 artículos/semana, Instagram: 5 posts/semana, Newsletter: quincenal"',
        helperText: "Cantidad y cadencia de publicaciones por canal",
        required: true,
        section: "planning",
      },
      {
        id: "key_dates",
        type: "textarea",
        label: "Fechas clave",
        placeholder:
          "Enumera fechas importantes: lanzamientos, eventos, temporadas, festivos relevantes, etc.",
        helperText: "Momentos específicos a considerar en la planificación",
        required: true,
        section: "planning",
      },
      {
        id: "content_workflow",
        type: "select",
        label: "Flujo de trabajo",
        options: [
          {
            value: "batch",
            label: "Creación por lotes (batch content creation)",
          },
          { value: "weekly", label: "Planificación semanal" },
          { value: "monthly", label: "Planificación mensual" },
          { value: "ongoing", label: "Continuo/Según necesidades" },
          { value: "custom", label: "Personalizado (especificar abajo)" },
        ],
        helperText: "Cómo se organizará la producción de contenido",
        section: "planning",
      },
      {
        id: "custom_workflow",
        type: "textarea",
        label: "Flujo personalizado",
        placeholder:
          'Describe tu metodología de trabajo si seleccionaste "Personalizado"',
        helperText: 'Completa solo si elegiste "Personalizado" arriba',
        advanced: true,
        section: "planning",
      },
      {
        id: "repurposing_strategy",
        type: "textarea",
        label: "Estrategia de reutilización",
        placeholder:
          "Describe cómo adaptarás contenido entre formatos y plataformas",
        helperText: "Plan para maximizar el uso de cada pieza de contenido",
        section: "planning",
      },
      {
        id: "success_metrics",
        type: "textarea",
        label: "Métricas de éxito",
        placeholder: "Enumera indicadores clave para medir resultados",
        helperText: "Cómo evaluarás el rendimiento de tu estrategia",
        required: true,
        section: "planning",
      },
    ],
    agents: [
      {
        id: "content-strategist",
        name: "Sofía Estrategia",
        avatar: "/agents/content-strategist.png",
        role: "Estratega de Contenidos",
        description:
          "Especialista en planificación editorial y estrategias de contenido transmedia",
      },
      {
        id: "marketing-manager",
        name: "Martín Marketing",
        avatar: "/agents/marketing-manager.png",
        role: "Gerente de Marketing Digital",
        description:
          "Experto en alinear contenidos con objetivos de negocio y medir resultados",
      },
    ],
    tips: [
      "Mantén un equilibrio entre contenido perenne (evergreen) y oportuno (timely)",
      "Planifica contenido relacionado con temporadas y fechas clave con suficiente antelación",
      "Establece un sistema claro de categorización por temas, formatos y etapas del funnel",
      "Considera la capacidad real de producción de tu equipo al establecer frecuencias",
      "Incluye espacio para contenido reactivo o de oportunidad no planificado",
    ],
    examples: [
      {
        title: "Calendario trimestral para SaaS B2B",
        description:
          "Enfoque en webinars mensuales con contenido derivado para blog y redes",
      },
      {
        title: "Plan anual para ecommerce estacional",
        description:
          "Estrategia basada en temporadas comerciales con picos de contenido promocional",
      },
      {
        title: "Calendario mensual para startup",
        description:
          "Mix de contenido educativo y de marca con frecuencia alta en redes sociales",
      },
    ],
  },

  // Social Media - Publicación para redes
  "social-post": {
    id: "social-post",
    title: "Publicación para Redes",
    description: "Crea contenido optimizado para cada plataforma social",
    icon: MessageSquare,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de tu publicación",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y objetivo",
        description: "Define quién leerá la publicación y su propósito",
        icon: Users,
      },
      {
        id: "platform",
        title: "Plataforma y formato",
        description: "Detalles específicos de la red social",
        icon: MessageSquare,
      },
      {
        id: "style",
        title: "Estilo y contenido",
        description: "Elementos de la publicación",
        icon: PenTool,
      },
    ],
    questions: [
      {
        id: "platform",
        type: "select",
        label: "Plataforma",
        placeholder: "Selecciona la red social",
        options: [
          { value: "instagram", label: "Instagram" },
          { value: "facebook", label: "Facebook" },
          { value: "twitter", label: "Twitter/X" },
          { value: "linkedin", label: "LinkedIn" },
          { value: "tiktok", label: "TikTok" },
          { value: "youtube", label: "YouTube" },
          { value: "pinterest", label: "Pinterest" },
          { value: "whatsapp", label: "WhatsApp" },
        ],
        required: true,
        section: "platform",
      },
      {
        id: "format",
        type: "select",
        label: "Formato de publicación",
        placeholder: "Elige un formato",
        options: [
          { value: "standard", label: "Post estándar" },
          { value: "carousel", label: "Carrusel/Múltiples imágenes" },
          { value: "story", label: "Historia" },
          { value: "reel", label: "Reel/Video corto" },
          { value: "live", label: "Contenido en vivo" },
        ],
        required: true,
        section: "platform",
      },
      {
        id: "post-purpose",
        type: "select",
        label: "Objetivo de la publicación",
        placeholder: "Selecciona el propósito principal",
        options: [
          { value: "awareness", label: "Crear awareness/visibilidad" },
          { value: "engagement", label: "Generar interacción" },
          { value: "education", label: "Educar a la audiencia" },
          { value: "entertainment", label: "Entretener" },
          { value: "conversion", label: "Convertir (compra, registro, etc.)" },
          { value: "customer-service", label: "Atención al cliente" },
          { value: "community", label: "Construir comunidad" },
        ],
        required: true,
        section: "audience",
      },
      {
        id: "target-audience",
        type: "textarea",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién está dirigida la publicación",
        helperText:
          "Incluye datos demográficos, intereses, necesidades y comportamientos",
        required: true,
        section: "audience",
      },
      {
        id: "main-topic",
        type: "text",
        label: "Tema principal",
        placeholder: "Tema central de la publicación",
        required: true,
        section: "basics",
      },
      {
        id: "key-message",
        type: "textarea",
        label: "Mensaje clave",
        placeholder: "Resumen del mensaje principal que quieres transmitir",
        helperText: "Sé claro y conciso, este es el marca de tu publicación",
        required: true,
        section: "basics",
      },
      {
        id: "tone",
        type: "tone",
        label: "Tono de voz",
        required: true,
        section: "style",
      },
      {
        id: "cta",
        type: "text",
        label: "Llamada a la acción (CTA)",
        placeholder: 'Ej: "Descubre más en link en bio", "Regístrate hoy"',
        helperText: "Acción que quieres que realice el usuario",
        section: "style",
      },
      {
        id: "hashtags",
        type: "textarea",
        label: "Hashtags",
        placeholder: "Escribe los hashtags principales para la publicación",
        helperText:
          "Incluye tanto hashtags populares como específicos de tu marca",
        section: "style",
      },
      {
        id: "visual-elements",
        type: "checkbox",
        label: "Elementos visuales a incluir",
        options: [
          { value: "image", label: "Imagen" },
          { value: "carousel", label: "Carrusel de imágenes" },
          { value: "video", label: "Video" },
          { value: "gif", label: "GIF" },
          { value: "infographic", label: "Infografía" },
          { value: "text-overlay", label: "Texto sobre imagen" },
          { value: "user-generated", label: "Contenido generado por usuarios" },
        ],
        section: "style",
      },
      {
        id: "post-structure",
        type: "select",
        label: "Estructura de publicación",
        options: [
          { value: "question", label: "Pregunta provocativa" },
          { value: "list", label: "Lista numerada/Puntos clave" },
          { value: "storytelling", label: "Narrativa/Historia personal" },
          { value: "quote", label: "Cita o testimonio" },
          { value: "tips", label: "Consejos prácticos" },
          { value: "behind-scenes", label: "Detrás de escenas" },
          { value: "announcement", label: "Anuncio o novedad" },
        ],
        section: "style",
      },
      {
        id: "additional-notes",
        type: "textarea",
        label: "Notas adicionales",
        placeholder: "Cualquier otra información relevante para la generación",
        section: "basics",
      },
    ],
    agents: [
      {
        id: "social-media-strategist",
        name: "Sandra Social",
        avatar: "/agents/social-media-strategist.png",
        role: "Estratega de Redes Sociales",
        description:
          "Especialista en crear contenido viral y maximizar engagement",
      },
      {
        id: "community-manager",
        name: "Carlos Comunidad",
        avatar: "/agents/community-manager.png",
        role: "Community Manager",
        description:
          "Experto en construir comunicación efectiva con tu audiencia",
      },
    ],
    tips: [
      "Personaliza el contenido según la plataforma - cada red social tiene su propio lenguaje",
      "Utiliza elementos visuales atractivos que capturen la atención al scrollear",
      "Incluye preguntas o llamados a la acción para fomentar la interacción",
      "Sé auténtico y mantén la voz de tu marca consistente",
      "Aprovecha las tendencias relevantes para tu audiencia y sector",
    ],
    examples: [
      {
        title: "Post de Instagram para producto",
        description:
          "Publicación visualmente atractiva con descripción persuasiva y CTA clara",
      },
      {
        title: "Publicación educativa para LinkedIn",
        description:
          "Contenido profesional que posiciona a la marca como autoridad en el sector",
      },
      {
        title: "Tweet promocional con incentivo",
        description:
          "Mensaje corto y directo con oferta de tiempo limitado que genera urgencia",
      },
    ],
  },

  // Social Media - Historia (Story)
  "social-story": {
    id: "social-story",
    title: "Historia (Story)",
    description: "Crea contenido efímero para Instagram, Facebook y WhatsApp",
    icon: Image,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales de tu historia",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y objetivo",
        description: "Define quién verá la historia y su propósito",
        icon: Users,
      },
      {
        id: "platform",
        title: "Plataforma y formato",
        description: "Plataforma y formato específico",
        icon: Image,
      },
      {
        id: "story-elements",
        title: "Elementos de la historia",
        description: "Componentes interactivos y visuales",
        icon: PenTool,
      },
    ],
    questions: [
      {
        id: "platform",
        type: "select",
        label: "Plataforma",
        placeholder: "Selecciona la red social",
        options: [
          { value: "instagram", label: "Instagram Stories" },
          { value: "facebook", label: "Facebook Stories" },
          { value: "whatsapp", label: "WhatsApp Status" },
          { value: "youtube", label: "YouTube Shorts" },
          { value: "snapchat", label: "Snapchat" },
        ],
        required: true,
        section: "platform",
      },
      {
        id: "story-purpose",
        type: "select",
        label: "Objetivo de la historia",
        placeholder: "Selecciona el propósito principal",
        options: [
          {
            value: "brand-awareness",
            label: "Aumentar reconocimiento de marca",
          },
          { value: "product-highlight", label: "Destacar producto/servicio" },
          { value: "drive-engagement", label: "Fomentar interacción" },
          {
            value: "drive-traffic",
            label: "Dirigir tráfico (swipe up/enlace)",
          },
          { value: "behind-scenes", label: "Mostrar detrás de escenas" },
          { value: "announcement", label: "Anuncio o lanzamiento" },
          { value: "poll-feedback", label: "Obtener opiniones/encuestas" },
          { value: "ugc-share", label: "Compartir contenido de usuarios" },
          { value: "event-coverage", label: "Cobertura de evento" },
        ],
        required: true,
        section: "audience",
      },
      {
        id: "target-audience",
        type: "textarea",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién está dirigida la historia",
        helperText:
          "Incluye datos demográficos, intereses y comportamientos relevantes",
        required: true,
        section: "audience",
      },
      {
        id: "main-topic",
        type: "text",
        label: "Tema central",
        placeholder: "Tema principal de la historia",
        required: true,
        section: "basics",
      },
      {
        id: "key-message",
        type: "textarea",
        label: "Mensaje clave",
        placeholder: "Mensaje principal que quieres transmitir",
        helperText:
          "Debe ser conciso y directo, ya que las historias son contenido rápido",
        required: true,
        section: "basics",
      },
      {
        id: "tone",
        type: "tone",
        label: "Tono de voz",
        required: true,
        section: "story-elements",
      },
      {
        id: "story-format",
        type: "select",
        label: "Formato de historia",
        options: [
          { value: "single-image", label: "Imagen única" },
          {
            value: "multiple-images",
            label: "Secuencia de imágenes (narrativa)",
          },
          { value: "video", label: "Video corto" },
          { value: "boomerang", label: "Boomerang/Loop" },
          { value: "text-only", label: "Solo texto con fondo" },
        ],
        required: true,
        section: "platform",
      },
      {
        id: "interactive-elements",
        type: "checkbox",
        label: "Elementos interactivos",
        options: [
          { value: "poll", label: "Encuesta" },
          { value: "quiz", label: "Quiz/Preguntas" },
          { value: "questions", label: "Caja de preguntas" },
          { value: "countdown", label: "Cuenta regresiva" },
          { value: "emoji-slider", label: "Slider de emoji" },
          { value: "swipe-up", label: "Swipe up/Enlace" },
          { value: "mentions", label: "Menciones" },
          { value: "stickers", label: "Stickers" },
          { value: "location", label: "Ubicación" },
        ],
        section: "story-elements",
      },
      {
        id: "visual-style",
        type: "select",
        label: "Estilo visual",
        options: [
          { value: "clean-minimal", label: "Limpio y minimalista" },
          { value: "colorful-vibrant", label: "Colorido y vibrante" },
          { value: "brand-consistent", label: "Consistente con la marca" },
          {
            value: "user-generated",
            label: "Estilo UGC (contenido de usuario)",
          },
          { value: "professional", label: "Profesional y pulido" },
          { value: "authentic-raw", label: "Auténtico y sin filtros" },
        ],
        section: "story-elements",
      },
      {
        id: "cta",
        type: "text",
        label: "Llamada a la acción (CTA)",
        placeholder: 'Ej: "Desliza hacia arriba", "Responde a la encuesta"',
        helperText: "Acción específica que quieres que realice el espectador",
        section: "story-elements",
      },
      {
        id: "sequence-length",
        type: "select",
        label: "Número de historias (secuencia)",
        options: [
          { value: "1", label: "1 historia" },
          { value: "2-3", label: "2-3 historias" },
          { value: "4-5", label: "4-5 historias" },
          { value: "6+", label: "6 o más historias" },
        ],
        section: "platform",
      },
      {
        id: "additional-notes",
        type: "textarea",
        label: "Notas adicionales",
        placeholder: "Cualquier otra información relevante",
        section: "basics",
      },
    ],
    agents: [
      {
        id: "story-specialist",
        name: "Samuel Story",
        avatar: "/agents/story-specialist.png",
        role: "Especialista en Stories",
        description: "Creador de contenido efímero que genera alto engagement",
      },
      {
        id: "social-media-designer",
        name: "Diana Diseño",
        avatar: "/agents/social-media-designer.png",
        role: "Diseñadora de Contenido Social",
        description: "Experta en narrativas visuales para plataformas sociales",
      },
    ],
    tips: [
      "Mantén cada historia simple y enfocada en un solo mensaje clave",
      "Usa el primer segundo para captar la atención antes que los usuarios pasen a la siguiente",
      "Aprovecha los elementos interactivos para aumentar el engagement",
      "Crea una secuencia con narrativa si usas múltiples historias consecutivas",
      "Siempre incluye una llamada a la acción clara, incluso si es implícita",
    ],
    examples: [
      {
        title: "Historia de lanzamiento de producto",
        description:
          "Secuencia con cuenta regresiva, revelación y CTA para comprar",
      },
      {
        title: "Encuesta de preferencias",
        description:
          "Historia interactiva que pide opinión a los seguidores sobre nuevas características",
      },
      {
        title: 'Historia tipo "Un día en la vida"',
        description: "Contenido auténtico mostrando el día a día en la empresa",
      },
    ],
  },

  // Redes Sociales - Calendario de contenido social
  "social-calendar": {
    id: "social-calendar",
    title: "Calendario de contenido social",
    description:
      "Planificación estratégica de publicaciones para redes sociales",
    icon: CalendarDays,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales para tu calendario",
        icon: FileText,
      },
      {
        id: "strategy",
        title: "Estrategia y objetivos",
        description: "Define el enfoque y metas de tu planificación",
        icon: Target,
      },
      {
        id: "content-mix",
        title: "Mix de contenido",
        description: "Combinación de tipos y temas de contenido",
        icon: LayoutDashboard,
      },
      {
        id: "logistics",
        title: "Logística y recursos",
        description: "Organización temporal y gestión de recursos",
        icon: Calendar,
      },
    ],
    questions: [
      {
        id: "period",
        type: "select",
        label: "Período de planificación",
        options: [
          { value: "week", label: "Semanal" },
          { value: "biweekly", label: "Quincenal" },
          { value: "month", label: "Mensual" },
          { value: "quarter", label: "Trimestral" },
        ],
        required: true,
        section: "basics",
      },
      {
        id: "platforms",
        type: "checkbox",
        label: "Plataformas a incluir",
        options: [
          { value: "instagram", label: "Instagram" },
          { value: "facebook", label: "Facebook" },
          { value: "twitter", label: "Twitter/X" },
          { value: "linkedin", label: "LinkedIn" },
          { value: "tiktok", label: "TikTok" },
          { value: "youtube", label: "YouTube" },
          { value: "pinterest", label: "Pinterest" },
        ],
        required: true,
        section: "basics",
      },
      {
        id: "primary-objective",
        type: "select",
        label: "Objetivo principal",
        options: [
          {
            value: "brand-awareness",
            label: "Aumentar reconocimiento de marca",
          },
          { value: "engagement", label: "Incrementar engagement" },
          { value: "community", label: "Construir comunidad" },
          { value: "leads", label: "Generar leads" },
          { value: "sales", label: "Impulsar ventas" },
          { value: "traffic", label: "Dirigir tráfico al sitio web" },
          { value: "loyalty", label: "Fidelizar clientes existentes" },
        ],
        required: true,
        section: "strategy",
      },
      {
        id: "target-audience",
        type: "textarea",
        label: "Audiencia objetivo",
        placeholder: "Describe las características de tu audiencia principal",
        helperText: "Incluye datos demográficos, intereses y comportamientos",
        required: true,
        section: "strategy",
      },
      {
        id: "key-themes",
        type: "textarea",
        label: "Temas clave",
        placeholder: "Lista los temas principales que quieres abordar",
        helperText: "Identifica 3-5 temas centrales para este período",
        required: true,
        section: "content-mix",
      },
      {
        id: "important-dates",
        type: "textarea",
        label: "Fechas importantes",
        placeholder: "Enumera fechas relevantes, eventos o lanzamientos",
        helperText:
          "Incluye fechas estacionales, festividades o hitos de la empresa",
        section: "logistics",
      },
      {
        id: "content-ratio",
        type: "checkbox",
        label: "Proporción de contenido",
        options: [
          {
            value: "educational",
            label: "Educativo (how-to, tips, tutoriales)",
          },
          {
            value: "promotional",
            label: "Promocional (productos, servicios, ofertas)",
          },
          {
            value: "entertaining",
            label: "Entretenimiento (divertido, inspirador)",
          },
          {
            value: "curated",
            label: "Curado (compartir contenido de terceros)",
          },
          {
            value: "user-generated",
            label: "Generado por usuarios (testimonios, reseñas)",
          },
          {
            value: "behind-scenes",
            label: "Detrás de escenas (equipo, procesos)",
          },
          {
            value: "interactive",
            label: "Interactivo (encuestas, preguntas, concursos)",
          },
        ],
        helperText: "Selecciona los tipos de contenido a incluir en tu mix",
        required: true,
        section: "content-mix",
      },
      {
        id: "posting-frequency",
        type: "select",
        label: "Frecuencia de publicación",
        options: [
          { value: "daily", label: "Diaria (7 veces por semana)" },
          { value: "weekdays", label: "Días laborables (5 veces por semana)" },
          { value: "alternate", label: "Días alternos (3-4 veces por semana)" },
          { value: "twice-week", label: "2 veces por semana" },
          { value: "weekly", label: "Semanal" },
          { value: "custom", label: "Personalizada (especificar en notas)" },
        ],
        required: true,
        section: "logistics",
      },
      {
        id: "best-times",
        type: "textarea",
        label: "Mejores horarios",
        placeholder: "Especifica los mejores momentos para publicar",
        helperText: "Basado en comportamiento de audiencia o análisis previos",
        section: "logistics",
      },
      {
        id: "content-series",
        type: "textarea",
        label: "Series de contenido",
        placeholder: "Describe series recurrentes a incluir",
        helperText: "Ej: #LunesDeMotivación, #TipDelJueves, etc.",
        section: "content-mix",
      },
      {
        id: "hashtag-strategy",
        type: "textarea",
        label: "Estrategia de hashtags",
        placeholder: "Enumera hashtags principales a utilizar",
        helperText:
          "Incluye hashtags de marca, categoría y trending relevantes",
        section: "strategy",
      },
      {
        id: "success-metrics",
        type: "checkbox",
        label: "Métricas de éxito",
        options: [
          { value: "reach", label: "Alcance/Impresiones" },
          {
            value: "engagement",
            label: "Engagement (likes, comentarios, shares)",
          },
          { value: "followers", label: "Crecimiento de seguidores" },
          { value: "clicks", label: "Clics/Tráfico al sitio" },
          { value: "conversions", label: "Conversiones/Ventas" },
          { value: "mentions", label: "Menciones/Etiquetas" },
          { value: "sentiment", label: "Sentimiento/Percepción" },
        ],
        helperText: "Selecciona las métricas para evaluar el éxito",
        section: "strategy",
      },
      {
        id: "resources-needed",
        type: "textarea",
        label: "Recursos necesarios",
        placeholder: "Lista los recursos para implementar este calendario",
        helperText:
          "Ej: diseñador gráfico, copywriter, herramientas de programación",
        section: "logistics",
      },
      {
        id: "additional-notes",
        type: "textarea",
        label: "Notas adicionales",
        placeholder: "Cualquier otra información relevante",
        section: "basics",
      },
    ],
    agents: [
      {
        id: "social-media-strategist",
        name: "Sandra Social",
        avatar: "/agents/social-media-strategist.png",
        role: "Estratega de Redes Sociales",
        description:
          "Especialista en planificación estratégica de contenido social",
      },
      {
        id: "content-calendar-expert",
        name: "Carlos Calendario",
        avatar: "/agents/content-calendar-expert.png",
        role: "Experto en Calendarios Editoriales",
        description:
          "Especialista en organización y optimización de flujos de contenido",
      },
    ],
    tips: [
      "Equilibra contenido planificado con espacio para oportunidades y tendencias emergentes",
      "Mantén coherencia en la voz de marca a través de todas las plataformas",
      "Adapta formatos y mensajes según las características específicas de cada plataforma",
      "Revisa y ajusta regularmente basándote en el rendimiento del contenido",
      "Planifica con anticipación pero mantén flexibilidad para responder a cambios",
    ],
    examples: [
      {
        title: "Calendario mensual para ecommerce",
        description:
          "Planificación con énfasis en productos destacados y ofertas especiales",
      },
      {
        title: "Plan trimestral para B2B",
        description:
          "Estrategia centrada en contenido educativo y generación de leads",
      },
      {
        title: "Calendario semanal para startup",
        description:
          "Mix ágil con contenido de construcción de marca y educación de mercado",
      },
    ],
  },

  // Redes Sociales - Contenido específico por plataforma
  "platform-specific": {
    id: "platform-specific",
    title: "Contenido específico por plataforma",
    description: "Optimizado para requisitos únicos de cada red social",
    icon: Router,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales del contenido",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y objetivos",
        description: "Define el público objetivo y propósito",
        icon: Users,
      },
      {
        id: "platform",
        title: "Plataforma específica",
        description: "Características particulares de la red social",
        icon: Router,
      },
      {
        id: "content",
        title: "Contenido y formato",
        description: "Detalles sobre el contenido a generar",
        icon: Layout,
      },
    ],
    questions: [
      {
        id: "platform",
        type: "select",
        label: "Plataforma principal",
        options: [
          { value: "instagram", label: "Instagram" },
          { value: "facebook", label: "Facebook" },
          { value: "twitter", label: "Twitter/X" },
          { value: "linkedin", label: "LinkedIn" },
          { value: "tiktok", label: "TikTok" },
          { value: "youtube", label: "YouTube" },
          { value: "pinterest", label: "Pinterest" },
        ],
        required: true,
        section: "platform",
      },
      {
        id: "content-purpose",
        type: "select",
        label: "Propósito del contenido",
        options: [
          { value: "awareness", label: "Generar visibilidad" },
          { value: "engagement", label: "Promover interacción" },
          { value: "education", label: "Educar a la audiencia" },
          { value: "conversion", label: "Impulsar conversiones" },
          { value: "loyalty", label: "Reforzar fidelidad" },
          { value: "community", label: "Construir comunidad" },
        ],
        required: true,
        section: "audience",
      },
      {
        id: "target-audience",
        type: "textarea",
        label: "Audiencia objetivo",
        placeholder: "Describe la audiencia específica en esta plataforma",
        helperText:
          "Considera las características demográficas y de comportamiento propias de esta red",
        required: true,
        section: "audience",
      },
      {
        id: "platform-features",
        type: "checkbox",
        label: "Características específicas a utilizar",
        options: [
          { value: "stories", label: "Stories/Historias" },
          { value: "reels", label: "Reels/Videos cortos" },
          { value: "igtv", label: "IGTV/Videos largos" },
          { value: "carousel", label: "Carrusel/Múltiples imágenes" },
          { value: "polls", label: "Encuestas/Sondeos" },
          { value: "lives", label: "Transmisiones en vivo" },
          { value: "groups", label: "Grupos/Comunidades" },
          { value: "shop", label: "Funciones de compra" },
          { value: "guides", label: "Guías/Colecciones" },
        ],
        helperText:
          "Selecciona las características de la plataforma que quieres aprovechar",
        section: "platform",
      },
      {
        id: "topic",
        type: "text",
        label: "Tema principal",
        placeholder: "Tema central del contenido",
        required: true,
        section: "basics",
      },
      {
        id: "key-message",
        type: "textarea",
        label: "Mensaje clave",
        placeholder: "Mensaje principal que quieres transmitir",
        helperText:
          "Adapta tu mensaje al estilo de comunicación de la plataforma",
        required: true,
        section: "basics",
      },
      {
        id: "tone",
        type: "tone",
        label: "Tono de voz",
        required: true,
        section: "content",
      },
      {
        id: "content-format",
        type: "select",
        label: "Formato principal",
        options: [
          { value: "image", label: "Imagen/Fotografía" },
          { value: "video", label: "Video" },
          { value: "text", label: "Texto" },
          { value: "infographic", label: "Infografía/Gráfico" },
          { value: "carousel", label: "Carrusel/Múltiples elementos" },
          { value: "audio", label: "Audio/Podcast" },
          { value: "mixed", label: "Formato mixto" },
        ],
        required: true,
        section: "content",
      },
      {
        id: "content-length",
        type: "select",
        label: "Extensión del contenido",
        options: [
          {
            value: "very-short",
            label: "Muy corto (< 15 segundos / 1-2 líneas)",
          },
          { value: "short", label: "Corto (15-60 segundos / párrafo breve)" },
          { value: "medium", label: "Medio (1-3 minutos / varios párrafos)" },
          { value: "long", label: "Largo (3-10 minutos / texto extenso)" },
          {
            value: "very-long",
            label: "Muy largo (> 10 minutos / artículo completo)",
          },
        ],
        section: "content",
      },
      {
        id: "content-elements",
        type: "checkbox",
        label: "Elementos a incluir",
        options: [
          { value: "hashtags", label: "Hashtags" },
          { value: "emojis", label: "Emojis" },
          { value: "mentions", label: "Menciones" },
          { value: "captions", label: "Subtítulos/Texto en imagen" },
          { value: "cta", label: "Llamada a la acción" },
          { value: "link", label: "Enlaces" },
          { value: "quotes", label: "Citas/Testimonios" },
          { value: "statistics", label: "Estadísticas/Datos" },
        ],
        section: "content",
      },
      {
        id: "specific-info",
        type: "textarea",
        label: "Información específica",
        placeholder: "Detalles particulares sobre el tema o mensaje",
        helperText: "Información clave que debe incluirse en el contenido",
        section: "basics",
      },
      {
        id: "platform-trends",
        type: "textarea",
        label: "Tendencias relevantes",
        placeholder: "Tendencias actuales en la plataforma a considerar",
        helperText:
          "Formatos, hashtags o desafíos populares que podrían incorporarse",
        section: "platform",
      },
      {
        id: "success-metrics",
        type: "select",
        label: "Métrica principal de éxito",
        options: [
          { value: "reach", label: "Alcance/Impresiones" },
          { value: "engagement", label: "Engagement (likes, comentarios)" },
          { value: "shares", label: "Compartidos/Retweets" },
          { value: "saves", label: "Guardados" },
          { value: "clicks", label: "Clics/Conversiones" },
          { value: "followers", label: "Nuevos seguidores" },
          { value: "views", label: "Visualizaciones" },
        ],
        section: "audience",
      },
      {
        id: "additional-notes",
        type: "textarea",
        label: "Notas adicionales",
        placeholder: "Cualquier otra información relevante",
        section: "basics",
      },
    ],
    agents: [
      {
        id: "platform-specialist",
        name: "Patricia Plataformas",
        avatar: "/agents/platform-specialist.png",
        role: "Especialista en Plataformas",
        description:
          "Experta en las particularidades y mejores prácticas de cada red social",
      },
      {
        id: "content-creator",
        name: "Cristian Creativo",
        avatar: "/agents/content-creator.png",
        role: "Creador de Contenido",
        description:
          "Especialista en adaptar mensajes al formato óptimo para cada plataforma",
      },
    ],
    tips: [
      "Adapta completamente el contenido a las especificaciones técnicas de cada plataforma",
      "Considera las expectativas y comportamientos específicos de los usuarios en cada red",
      "Aprovecha las funcionalidades nativas y tendencias actuales de la plataforma",
      "Mantén la autenticidad de tu marca mientras te adaptas al lenguaje de cada red",
      "Revisa regularmente las actualizaciones de la plataforma para ajustar tu estrategia",
    ],
    examples: [
      {
        title: "Carrusel educativo para Instagram",
        description:
          "Serie de imágenes con datos visuales y explicaciones breves sobre un tema",
      },
      {
        title: "Artículo de liderazgo para LinkedIn",
        description:
          "Contenido extenso con insights profesionales y llamada a la conversación",
      },
      {
        title: "Video corto para TikTok",
        description:
          "Contenido dinámico siguiendo tendencias actuales con mensaje de marca sutil",
      },
    ],
  },
};

// No combinamos todos los formularios de una vez para no cargar todo en memoria
// En su lugar, usamos una función que busca el formulario en los diferentes módulos
const contentFormData: Record<string, ContentFormData> = {
  ...baseContentFormData,
};

// Crear una función para generar un formulario genérico si no existe uno específico
export const getGenericForm = (
  contentTypeId: string,
): ContentFormData | null => {
  const subtype = findContentSubtypeById(contentTypeId);
  const parentType = findParentContentType(contentTypeId);

  if (!subtype || !parentType) return null;

  // Crear un formulario genérico basado en el tipo y subtipo
  return {
    id: contentTypeId,
    title: subtype.name,
    description: subtype.description || `Formulario para crear ${subtype.name}`,
    icon: subtype.icon || parentType.icon,
    sections: [
      {
        id: "basics",
        title: "Información básica",
        description: "Detalles fundamentales para tu contenido",
        icon: FileText,
      },
      {
        id: "audience",
        title: "Audiencia y objetivos",
        description: "Define para quién escribes y qué quieres lograr",
        icon: Users,
      },
      {
        id: "content",
        title: "Contenido y estructura",
        description: "Detalles sobre el contenido a generar",
        icon: LayoutDashboard,
      },
    ],
    questions: [
      {
        id: "title",
        type: "text",
        label: "Título",
        placeholder: `Ej: Título para ${subtype.name}`,
        helperText: "Un título descriptivo y atractivo para tu contenido",
        required: true,
        section: "basics",
      },
      {
        id: "description",
        type: "textarea",
        label: "Descripción",
        placeholder: "Describe brevemente el contenido que deseas generar",
        helperText: "Resume en 2-3 frases el enfoque principal",
        required: true,
        section: "basics",
      },
      {
        id: "target-audience",
        type: "audience",
        label: "Audiencia objetivo",
        placeholder: "Describe a quién va dirigido este contenido",
        helperText: "Define las características de tu audiencia ideal",
        required: true,
        section: "audience",
      },
      {
        id: "content-purpose",
        type: "select",
        label: "Propósito del contenido",
        options: [
          { value: "inform", label: "Informar" },
          { value: "educate", label: "Educar" },
          { value: "persuade", label: "Persuadir" },
          { value: "entertain", label: "Entretener" },
          { value: "convert", label: "Convertir" },
          { value: "engage", label: "Generar interacción" },
        ],
        required: true,
        section: "audience",
      },
      {
        id: "tone",
        type: "tone",
        label: "Tono de voz",
        required: true,
        section: "content",
      },
      {
        id: "key-points",
        type: "textarea",
        label: "Puntos clave",
        placeholder: "Enumera los puntos principales que quieres incluir",
        helperText: "Lista los mensajes o ideas más importantes",
        required: true,
        section: "content",
      },
      {
        id: "additional-notes",
        type: "textarea",
        label: "Notas adicionales",
        placeholder: "Cualquier otra información relevante",
        section: "content",
      },
    ],
    agents: [
      {
        id: "content-specialist",
        name: "Especialista de Contenido",
        avatar: "/agents/content-specialist.png",
        role: "Especialista en Contenido",
        description: `Experto en crear ${subtype.name} efectivo y persuasivo`,
      },
      {
        id: "audience-analyst",
        name: "Analista de Audiencia",
        avatar: "/agents/audience-analyst.png",
        role: "Analista de Audiencia",
        description:
          "Especialista en adaptación de mensajes a la audiencia objetivo",
      },
    ],
    tips: [
      "Sé específico y claro en tu descripción para obtener mejores resultados",
      "Define bien tu audiencia para que el contenido sea relevante",
      "Incluye ejemplos o referencias si tienes un estilo específico en mente",
    ],
    examples: [
      {
        title: `Ejemplo de ${subtype.name}`,
        description:
          "Contenido optimizado para el objetivo y audiencia especificados",
      },
    ],
  };
};

// Obtener un formulario por ID de forma eficiente
export const getContentFormById = (formId: string): ContentFormData | null => {
  // 1. Primero verificamos en el objeto base, que es el más pequeño
  if (contentFormData[formId]) {
    return contentFormData[formId];
  }

  // 2. Si no existe en el objeto base, buscamos en módulos específicos
  // Esto evita cargar todos los datos de una vez
  try {
    // Buscar en el primer módulo extra
    if (contentFormQuestionsExtra[formId]) {
      return contentFormQuestionsExtra[formId];
    }

    // Buscar en el segundo módulo extra
    if (contentFormQuestionsExtra2[formId]) {
      return contentFormQuestionsExtra2[formId];
    }

    // Buscar en el tercer módulo extra
    if (contentFormQuestionsExtra3[formId]) {
      return contentFormQuestionsExtra3[formId];
    }

    // Buscar en el cuarto módulo extra
    if (contentFormQuestionsExtra4[formId]) {
      return contentFormQuestionsExtra4[formId];
    }
  } catch (error) {
    console.warn("Error al buscar formulario en módulos extra:", error);
  }

  // 3. Si no existe en ninguno de los módulos, creamos un formulario genérico
  return getGenericForm(formId);
};

export default contentFormData;
