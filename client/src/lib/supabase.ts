import { createClient } from '@supabase/supabase-js'

// Configuración de Supabase
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'placeholder-key'

if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
  console.warn('Supabase URL or Anon Key not found in environment variables - using placeholder values')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Tipos para Marca
export interface Marca {
  id: string
  created_at: string
  updated_at: string
  
  // Información básica
  brand_name: string
  website?: string
  industry: string
  
  // Identidad visual
  logo_url?: string
  primary_color: string
  secondary_color: string
  
  // Audiencia y tono
  target_audience: string
  tone: string
  personality: string[]
  
  // Posicionamiento
  description: string
  unique_value: string
  competitors?: string
  
  // Documentos y ejemplos
  documents?: string[]
  examples?: string
  
  // Metadata
  status: 'draft' | 'active' | 'archived'
  campaigns_count: number
  assets_count: number
  
  // Usuario
  user_id?: string
}

export interface CreateMarcaData {
  brand_name: string
  website?: string
  industry: string
  logo_url?: string
  primary_color: string
  secondary_color: string
  target_audience: string
  tone: string
  personality: string[]
  description: string
  unique_value: string
  competitors?: string
  documents?: string[]
  examples?: string
  user_id?: string
}

export interface UpdateMarcaData extends Partial<CreateMarcaData> {
  id: string
}
