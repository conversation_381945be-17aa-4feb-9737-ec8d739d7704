/**
 * Página para aplicar estilo de referencia
 * Permite aplicar el estilo de una imagen de referencia a una nueva generación
 * Basado en el patrón de poster-creator, meme-creator y ad-creator
 */

import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import {
  Palette,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  HeartOff,
  Upload,
  X,
  ImageIcon,
  Settings,
  Zap,
  Eye,
  EyeOff,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  applyStyleReference,
  StyleReferenceOptions,
} from "@/services/style-reference-service";

// Tipos para el estado de la aplicación
interface GeneratedStyleReference {
  id: string;
  image_url: string;
  reference_url: string;
  prompt: string;
  metadata?: any;
  timestamp: number;
}

interface SavedStyleReference {
  id: string;
  image_url: string;
  reference_url: string;
  prompt: string;
  metadata?: any;
  timestamp: number;
}

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Utilidades para estilos de referencia guardados
const SAVED_STYLE_REFERENCES_KEY = "emma-saved-style-references";

function createSavedStyleReference(data: {
  image_url: string;
  reference_url: string;
  prompt: string;
  metadata?: any;
}): SavedStyleReference {
  return {
    id: `style-ref-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: Date.now(),
    ...data,
  };
}

function isStyleReferenceSaved(imageUrl: string, savedStyleReferences: SavedStyleReference[]): boolean {
  return savedStyleReferences.some(styleRef => styleRef.image_url === imageUrl);
}

export default function StyleReferencePage() {
  // Estados principales
  const [currentStyleReference, setCurrentStyleReference] = useState<GeneratedStyleReference | null>(null);
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para imagen de referencia
  const [referenceImage, setReferenceImage] = useState<File | null>(null);
  const [referenceImagePreview, setReferenceImagePreview] = useState<string | null>(null);

  // Estados para configuración
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [fidelity, setFidelity] = useState([0.5]);
  const [seed, setSeed] = useState(0);
  const [outputFormat, setOutputFormat] = useState('png');
  const [stylePreset, setStylePreset] = useState<string>('');

  // Estados para favoritos
  const [savedStyleReferences, setSavedStyleReferences] = useLocalStorage<SavedStyleReference[]>(SAVED_STYLE_REFERENCES_KEY, []);
  const [currentStyleReferenceSaved, setCurrentStyleReferenceSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo para estilo de referencia
  const examplePrompts = [
    "Una pintura al óleo de un paisaje montañoso con el estilo de la imagen de referencia",
    "Retrato de una persona joven con la técnica artística de la referencia",
    "Arquitectura moderna interpretada con el estilo visual de la imagen",
    "Naturaleza muerta con flores siguiendo la paleta de colores de referencia",
    "Escena urbana nocturna con la atmósfera de la imagen de referencia",
    "Animal fantástico creado con la técnica artística de la referencia"
  ];

  // Opciones de relación de aspecto
  const aspectRatioOptions = [
    { value: "1:1", label: "Cuadrado (1:1)", icon: "⬜" },
    { value: "16:9", label: "Horizontal (16:9)", icon: "▭" },
    { value: "9:16", label: "Vertical (9:16)", icon: "▯" },
    { value: "3:2", label: "Foto (3:2)", icon: "📷" },
    { value: "2:3", label: "Retrato (2:3)", icon: "🖼️" }
  ];

  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (currentStyleReference) {
      setCurrentStyleReferenceSaved(isStyleReferenceSaved(currentStyleReference.image_url, savedStyleReferences));
    }
  }, [currentStyleReference, savedStyleReferences]);

  // Función para manejar la carga de imagen de referencia
  const handleReferenceImageUpload = (file: File) => {
    setReferenceImage(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setReferenceImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Función para generar con estilo de referencia
  const handleGenerate = async () => {
    if (!prompt.trim() || !referenceImage) {
      toast({
        title: "Datos requeridos",
        description: "Necesitas una imagen de referencia y un prompt",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: StyleReferenceOptions = {
        image: referenceImage,
        prompt: prompt.trim(),
        negativePrompt: negativePrompt.trim() || undefined,
        aspectRatio: aspectRatio as any,
        fidelity: fidelity[0],
        seed: seed === 0 ? undefined : seed,
        outputFormat: outputFormat as any,
        stylePreset: stylePreset || undefined,
      };

      const imageUrl = await applyStyleReference(options);

      const newStyleReference: GeneratedStyleReference = {
        id: Date.now().toString(),
        image_url: imageUrl,
        reference_url: referenceImagePreview!,
        prompt,
        metadata: {
          aspectRatio,
          fidelity: fidelity[0],
          seed: seed === 0 ? undefined : seed,
          outputFormat,
          stylePreset: stylePreset || undefined,
        },
        timestamp: Date.now(),
      };

      setCurrentStyleReference(newStyleReference);

      toast({
        title: "¡Estilo aplicado exitosamente!",
        description: "Tu nueva imagen está lista",
      });
    } catch (error) {
      console.error("Error applying style reference:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al aplicar el estilo de referencia",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para alternar favoritos
  const handleToggleFavorite = () => {
    if (!currentStyleReference) return;

    if (currentStyleReferenceSaved) {
      // Remover de favoritos
      const updatedStyleReferences = savedStyleReferences.filter(
        styleRef => styleRef.image_url !== currentStyleReference.image_url
      );
      setSavedStyleReferences(updatedStyleReferences);
      setCurrentStyleReferenceSaved(false);

      toast({
        title: "Removido de favoritos",
        description: "La imagen ha sido removida de tus favoritos.",
      });
    } else {
      // Agregar a favoritos
      const styleRefData = {
        image_url: currentStyleReference.image_url,
        reference_url: currentStyleReference.reference_url,
        prompt: currentStyleReference.prompt,
        metadata: currentStyleReference.metadata,
      };

      const newStyleReference = createSavedStyleReference(styleRefData);
      const updatedStyleReferences = [newStyleReference, ...savedStyleReferences].slice(0, 50); // Limitar a 50

      setSavedStyleReferences(updatedStyleReferences);
      setCurrentStyleReferenceSaved(true);

      toast({
        title: "❤️ ¡Guardado en favoritos!",
        description: "Imagen guardada exitosamente en tus favoritos.",
      });
    }
  };

  // Función para descargar imagen
  const handleDownload = async () => {
    if (!currentStyleReference?.image_url) return;

    try {
      const response = await fetch(currentStyleReference.image_url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `style-reference-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Descarga iniciada",
        description: "La imagen se está descargando.",
      });
    } catch (error) {
      toast({
        title: "Error en la descarga",
        description: "No se pudo descargar la imagen.",
        variant: "destructive",
      });
    }
  };

  // Función para compartir (copiar al portapapeles)
  const handleShare = async () => {
    if (!currentStyleReference?.image_url) return;

    try {
      const response = await fetch(currentStyleReference.image_url);
      const blob = await response.blob();

      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);

      toast({
        title: "¡Copiado al portapapeles!",
        description: "La imagen ha sido copiada al portapapeles.",
      });
    } catch (error) {
      toast({
        title: "Error al copiar",
        description: "No se pudo copiar la imagen al portapapeles.",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardLayout pageTitle="Aplicar Estilo de Referencia">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Palette className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Aplicar Estilo de Referencia
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Aplica el estilo de una imagen de referencia a tu nueva creación con IA. Transfiere técnicas artísticas y estilos visuales.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="w-3 h-3 mr-1" />
                  Transferencia de estilo
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Palette className="w-3 h-3 mr-1" />
                  Técnicas artísticas
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Wand2 className="w-3 h-3 mr-1" />
                  IA avanzada
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Configura y aplica el estilo de referencia
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                    {/* Imagen de referencia */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imagen de Referencia</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleReferenceImageUpload(file);
                            }
                          }}
                          className="hidden"
                        />
                        {referenceImagePreview ? (
                          <div className="space-y-3">
                            <img
                              src={referenceImagePreview}
                              alt="Imagen de referencia"
                              className="max-w-full max-h-32 mx-auto rounded-lg object-contain"
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setReferenceImage(null);
                                setReferenceImagePreview(null);
                              }}
                            >
                              <X className="h-3 w-3 mr-1" />
                              Cambiar imagen
                            </Button>
                          </div>
                        ) : (
                          <>
                            <Button
                              variant="outline"
                              onClick={() => fileInputRef.current?.click()}
                              className="mb-2"
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Subir Imagen
                            </Button>
                            <p className="text-xs text-gray-500">
                              PNG, JPG, WebP hasta 10MB
                            </p>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Prompt */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la Nueva Imagen</Label>
                      <Textarea
                        placeholder="Describe qué quieres crear con el estilo de la imagen de referencia..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    {/* Prompt negativo */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Prompt Negativo (Opcional)</Label>
                      <Textarea
                        placeholder="Describe qué NO quieres en la imagen..."
                        value={negativePrompt}
                        onChange={(e) => setNegativePrompt(e.target.value)}
                        className="min-h-[60px] resize-none"
                      />
                    </div>



                    {/* Fidelidad */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">
                        Fidelidad al Estilo: {fidelity[0].toFixed(1)}
                      </Label>
                      <Slider
                        value={fidelity}
                        onValueChange={setFidelity}
                        max={1}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Más creativo</span>
                        <span>Más fiel</span>
                      </div>
                    </div>

                    {/* Botón de generar */}
                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim() || !referenceImage}
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Aplicando Estilo...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Aplicar Estilo de Referencia
                        </>
                      )}
                    </Button>

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Área de Visualización */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="latest" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Última Generación
                </TabsTrigger>
                <TabsTrigger value="saved" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Guardados ({savedStyleReferences.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-6">
                {/* Imagen Generada */}
                {currentStyleReference && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          <Zap className="h-5 w-5 text-purple-600" />
                          Estilo Aplicado
                        </CardTitle>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleToggleFavorite}
                            className={currentStyleReferenceSaved ? "text-red-600 border-red-200" : ""}
                          >
                            {currentStyleReferenceSaved ? (
                              <Heart className="h-4 w-4 fill-current" />
                            ) : (
                              <HeartOff className="h-4 w-4" />
                            )}
                          </Button>
                          <Button variant="outline" size="sm" onClick={handleShare}>
                            <Share2 className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={handleDownload}>
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Imagen de referencia */}
                        <div>
                          <Label className="text-sm font-medium mb-2 block">Imagen de Referencia</Label>
                          <img
                            src={currentStyleReference.reference_url}
                            alt="Imagen de referencia"
                            className="w-full rounded-lg shadow-md"
                          />
                        </div>

                        {/* Imagen generada */}
                        <div>
                          <Label className="text-sm font-medium mb-2 block">Resultado</Label>
                          <div className="relative group">
                            <img
                              src={currentStyleReference.image_url}
                              alt="Imagen generada"
                              className="w-full rounded-lg shadow-lg"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                          </div>
                        </div>
                      </div>

                      {/* Información del prompt */}
                      <div className="mt-4 space-y-2">
                        <div className="flex items-start gap-2">
                          <Badge variant="secondary" className="mt-0.5">Prompt</Badge>
                          <p className="text-sm text-gray-600 flex-1">{currentStyleReference.prompt}</p>
                        </div>

                        {currentStyleReference.metadata && (
                          <div className="flex items-center gap-2 text-xs text-gray-400">
                            <span>Fidelidad: {currentStyleReference.metadata.fidelity}</span>
                            <span>• Aspecto: {currentStyleReference.metadata.aspectRatio}</span>
                            {currentStyleReference.metadata.seed && (
                              <span>• Seed: {currentStyleReference.metadata.seed}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Mensaje cuando no hay imagen generada */}
                {!currentStyleReference && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="p-12 text-center">
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Palette className="h-8 w-8 text-purple-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        Aplica un Estilo de Referencia
                      </h3>
                      <p className="text-gray-600 mb-4">
                        Sube una imagen de referencia y describe qué quieres crear para aplicar su estilo artístico.
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        <Badge variant="outline">Transferencia de estilo</Badge>
                        <Badge variant="outline">Técnicas artísticas</Badge>
                        <Badge variant="outline">IA creativa</Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="saved" className="space-y-6">
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Heart className="h-5 w-5 text-primary" />
                      Estilos de Referencia Guardados
                      <Badge variant="secondary">{savedStyleReferences.length}</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {savedStyleReferences.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {savedStyleReferences.map((savedStyleRef) => (
                          <div
                            key={savedStyleRef.id}
                            className="bg-white border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                          >
                            <div className="relative">
                              <img
                                src={savedStyleRef.image_url}
                                alt="Imagen guardada"
                                className="w-full h-48 object-cover"
                              />
                              <div className="absolute top-2 right-2">
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  onClick={() => {
                                    const updatedStyleReferences = savedStyleReferences.filter(
                                      styleRef => styleRef.id !== savedStyleRef.id
                                    );
                                    setSavedStyleReferences(updatedStyleReferences);
                                    toast({
                                      title: "Removido de favoritos",
                                      description: "La imagen ha sido removida de tus favoritos.",
                                    });
                                  }}
                                  className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <div className="p-4">
                              <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                                {savedStyleRef.prompt}
                              </p>
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-gray-400">
                                  {new Date(savedStyleRef.timestamp).toLocaleDateString()}
                                </span>
                                <div className="flex gap-1">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={async () => {
                                      try {
                                        const response = await fetch(savedStyleRef.image_url);
                                        const blob = await response.blob();
                                        const url = window.URL.createObjectURL(blob);
                                        const a = document.createElement("a");
                                        a.href = url;
                                        a.download = `style-reference-${savedStyleRef.id}.png`;
                                        document.body.appendChild(a);
                                        a.click();
                                        window.URL.revokeObjectURL(url);
                                        document.body.removeChild(a);
                                      } catch (error) {
                                        toast({
                                          title: "Error en la descarga",
                                          description: "No se pudo descargar la imagen.",
                                          variant: "destructive",
                                        });
                                      }
                                    }}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Download className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Heart className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          No hay estilos guardados
                        </h3>
                        <p className="text-gray-600">
                          Los estilos de referencia que marques como favoritos aparecerán aquí.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
}
