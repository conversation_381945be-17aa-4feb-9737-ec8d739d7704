"use client";

import Re<PERSON>, { useState, useR<PERSON>, use<PERSON><PERSON>back } from "react";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowLeft,
  Upload,
  Wand2,
  Download,
  ImagePlus,
  Loader2,
  CheckCircle,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { convertSketchToImage } from "@/services/sketch-to-image-service";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { CardDescription } from "@/components/ui/card";
import { Settings } from "lucide-react";

// Interfaz para imágenes guardadas
interface SavedSketch {
  id: string;
  originalUrl: string;
  processedUrl: string;
  originalFilename: string;
  prompt: string;
  negativePrompt?: string;
  controlStrength: number;
  stylePreset?: string;
  outputFormat: string;
  timestamp: number;
  isFavorite: boolean;
}

// Custom hook para localStorage que funciona mejor en Safari
const useLocalStorage = <T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  // Estado para almacenar nuestro valor
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Función para actualizar el estado y localStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Permitir que value sea una función para que tengamos la misma API que useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      // Guardar en estado
      setStoredValue(valueToStore);

      // Guardar en localStorage
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));

        // Disparar evento personalizado para sincronización entre componentes
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Funciones para manejar imágenes guardadas
const SAVED_SKETCHES_KEY = 'emma_saved_sketch_to_image';

// Funciones simplificadas para trabajar con el hook
const createSavedSketch = (sketchData: Omit<SavedSketch, 'id' | 'timestamp' | 'isFavorite'>): SavedSketch => {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 1000000).toString();

  return {
    ...sketchData,
    id: `sketch_${timestamp}_${randomPart}`,
    timestamp: timestamp,
    isFavorite: true,
  };
};

const isSketchSaved = (imageUrl: string, savedSketches: SavedSketch[]): boolean => {
  return savedSketches.some(sketch => sketch.processedUrl === imageUrl);
};

// Estilos para la herramienta de dibujo
const canvasStyles: React.CSSProperties = {
  border: "2px dashed rgba(0, 0, 0, 0.2)",
  cursor: "crosshair",
  touchAction: "none",
  backgroundColor: "white",
};

// Opciones de estilo predefinidas
const stylePresets = [
  { value: "enhance", label: "Mejorar (Por defecto)" },
  { value: "3d-model", label: "Modelo 3D" },
  { value: "analog-film", label: "Película Analógica" },
  { value: "anime", label: "Anime" },
  { value: "cinematic", label: "Cinematográfico" },
  { value: "comic-book", label: "Cómic" },
  { value: "digital-art", label: "Arte Digital" },
  { value: "fantasy-art", label: "Arte Fantástico" },
  { value: "isometric", label: "Isométrico" },
  { value: "line-art", label: "Arte Lineal" },
  { value: "low-poly", label: "Polígonos Bajos" },
  { value: "modeling-compound", label: "Modelado Compuesto" },
  { value: "neon-punk", label: "Neon Punk" },
  { value: "origami", label: "Origami" },
  { value: "photographic", label: "Fotográfico" },
  { value: "pixel-art", label: "Pixel Art" },
  { value: "tile-texture", label: "Textura de Azulejos" },
];

// Opciones de formato de salida
const outputFormats = [
  { value: "png", label: "PNG" },
  { value: "jpeg", label: "JPEG" },
  { value: "webp", label: "WebP" },
];

export default function SketchToImagePage() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();

  // Estado para la imagen
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [originalName, setOriginalName] = useState<string>("");

  // Estados para las opciones
  const [prompt, setPrompt] = useState<string>("");
  const [negativePrompt, setNegativePrompt] = useState<string>("");
  const [controlStrength, setControlStrength] = useState<number>(0.7);
  const [stylePreset, setStylePreset] = useState<string>("enhance");
  const [outputFormat, setOutputFormat] = useState<string>("png");
  const [seed, setSeed] = useState<number>(0);

  // Referencias para el canvas y contexto de dibujo
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<CanvasRenderingContext2D | null>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);

  // Estados para la funcionalidad de guardados
  const [savedSketches, setSavedSketches] = useLocalStorage<SavedSketch[]>(SAVED_SKETCHES_KEY, []);
  const [currentSketchSaved, setCurrentSketchSaved] = useState(false);
  const [activeTab, setActiveTab] = useState("latest");

  // Inicializar el canvas
  const initCanvas = (canvas: HTMLCanvasElement) => {
    const context = canvas.getContext("2d");
    if (context) {
      // Configurar el contexto para dibujo
      context.lineCap = "round";
      context.strokeStyle = "black";
      context.lineWidth = 5;
      contextRef.current = context;

      // Limpiar el canvas (fondo blanco)
      context.fillStyle = "white";
      context.fillRect(0, 0, canvas.width, canvas.height);
    }
  };

  // Efecto para inicializar el canvas cuando el componente se monta
  React.useEffect(() => {
    if (canvasRef.current) {
      initCanvas(canvasRef.current);
    }
  }, []);

  // Funciones para dibujar en el canvas
  const startDrawing = useCallback((x: number, y: number) => {
    if (!contextRef.current) return;

    contextRef.current.beginPath();
    contextRef.current.moveTo(x, y);
    setIsDrawing(true);
  }, []);

  const draw = useCallback(
    (x: number, y: number) => {
      if (!contextRef.current || !isDrawing) return;

      contextRef.current.lineTo(x, y);
      contextRef.current.stroke();
    },
    [isDrawing],
  );

  const stopDrawing = useCallback(() => {
    if (!contextRef.current) return;

    contextRef.current.closePath();
    setIsDrawing(false);

    // Actualizar la imagen del canvas como archivo
    if (canvasRef.current) {
      canvasRef.current.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], "sketch.png", { type: "image/png" });
          setImageFile(file);
          setImageSrc(URL.createObjectURL(blob));
          setOriginalName("sketch.png");
        }
      }, "image/png");
    }
  }, []);

  // Manejadores de eventos para el canvas
  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!canvasRef.current) return;

      const rect = canvasRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      startDrawing(x, y);
    },
    [startDrawing],
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!canvasRef.current || !isDrawing) return;

      const rect = canvasRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      draw(x, y);
    },
    [draw, isDrawing],
  );

  const handleMouseUp = useCallback(() => {
    stopDrawing();
  }, [stopDrawing]);

  const handleMouseLeave = useCallback(() => {
    stopDrawing();
  }, [stopDrawing]);

  // Manejadores para eventos táctiles
  const handleTouchStart = useCallback(
    (e: React.TouchEvent<HTMLCanvasElement>) => {
      if (!canvasRef.current) return;
      e.preventDefault();

      const rect = canvasRef.current.getBoundingClientRect();
      const x = e.touches[0].clientX - rect.left;
      const y = e.touches[0].clientY - rect.top;
      startDrawing(x, y);
    },
    [startDrawing],
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent<HTMLCanvasElement>) => {
      if (!canvasRef.current || !isDrawing) return;
      e.preventDefault();

      const rect = canvasRef.current.getBoundingClientRect();
      const x = e.touches[0].clientX - rect.left;
      const y = e.touches[0].clientY - rect.top;
      draw(x, y);
    },
    [draw, isDrawing],
  );

  const handleTouchEnd = useCallback(() => {
    stopDrawing();
  }, [stopDrawing]);

  // Limpiar el canvas
  const clearCanvas = useCallback(() => {
    if (!canvasRef.current || !contextRef.current) return;

    contextRef.current.fillStyle = "white";
    contextRef.current.fillRect(
      0,
      0,
      canvasRef.current.width,
      canvasRef.current.height,
    );
    setImageFile(null);
    setImageSrc(null);
    setOriginalName("");
  }, []);

  // Subir una imagen
  const handleImageUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const file = e.target.files[0];

        // Lista de tipos MIME permitidos
        const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
        const isHeic =
          file.name.toLowerCase().endsWith(".heic") ||
          file.name.toLowerCase().endsWith(".heif") ||
          file.type === "image/heic" ||
          file.type === "image/heif";

        // Verificar el tipo de archivo con validación más estricta
        if (!allowedTypes.includes(file.type) && !isHeic) {
          toast({
            title: "Formato incorrecto",
            description:
              "Solo se permiten imágenes en formato JPG, PNG, WebP o HEIC/HEIF.",
            variant: "destructive",
          });
          return;
        }

        // Mensaje informativo para HEIC (ahora se admite, pero con conversión)
        if (isHeic) {
          toast({
            title: "Procesando imagen HEIC/HEIF",
            description:
              "Estamos convirtiendo tu imagen a JPG automáticamente. Esto puede tardar unos segundos más de lo normal.",
            duration: 5000,
          });
        }

        // Verificar tamaño máximo (10MB)
        const MAX_SIZE = 10 * 1024 * 1024;
        if (file.size > MAX_SIZE) {
          toast({
            title: "Archivo demasiado grande",
            description: `La imagen excede el límite de 10MB. Por favor, reduce su tamaño o usa otra imagen.`,
            variant: "destructive",
          });
          return;
        }

        // Usar la imagen directamente
        setImageFile(file);
        setImageSrc(URL.createObjectURL(file));
        setOriginalName(file.name);

        toast({
          title: "Imagen cargada",
          description:
            'Tu imagen se ha cargado correctamente. Ahora puedes convertirla usando el botón "Generar Imagen"',
        });
      }
    },
    [toast],
  );

  // Generar la imagen a partir del boceto
  const generateImage = async () => {
    if (!imageFile) {
      toast({
        title: "Error",
        description: "Primero dibuja o sube una imagen",
        variant: "destructive",
      });
      return;
    }

    if (!prompt) {
      toast({
        title: "Error",
        description: "El prompt es obligatorio",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const response = await convertSketchToImage(imageFile, {
        prompt,
        negativePrompt: negativePrompt || undefined,
        controlStrength,
        seed: seed || undefined,
        outputFormat: outputFormat as "jpeg" | "png" | "webp",
        stylePreset: stylePreset as any,
      });

      if (response.success && response.imageUrl) {
        setResultImage(response.imageUrl);
        toast({
          title: "✨ ¡Imagen generada!",
          description: "Tu boceto se ha convertido en una imagen increíble.",
        });
      } else {
        toast({
          title: "Error",
          description: response.error || "Error al generar la imagen",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error al generar imagen:", error);

      // Mensaje personalizado basado en el tipo de error
      let errorMessage = "Ocurrió un error al procesar tu solicitud";

      if (error.response) {
        const status = error.response.status;
        if (status === 500) {
          errorMessage = "Error en el servidor. Verifica el formato de imagen y el tamaño.";
        } else if (status === 413) {
          errorMessage = "La imagen es demasiado grande. El límite es de 10MB.";
        } else if (status === 400) {
          errorMessage = "Parámetros incorrectos. Verifica tus opciones.";
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Descargar la imagen resultante
  const downloadResult = () => {
    if (!resultImage) return;

    console.log("Intentando descargar imagen:", resultImage);

    // Asegurarnos de que la URL sea absoluta
    const fullUrl = resultImage;

    // Crear un elemento de enlace para descargar
    const link = document.createElement("a");
    link.href = fullUrl;
    link.download = `sketch-to-image.${outputFormat}`;

    // Método directo usando fetch para obtener la imagen
    fetch(fullUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error(
            `Error de red: ${response.status} ${response.statusText}`,
          );
        }
        return response.blob();
      })
      .then((blob) => {
        console.log("Imagen obtenida correctamente, tamaño:", blob.size);
        const blobUrl = URL.createObjectURL(blob);
        link.href = blobUrl;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl); // Liberar recursos

        toast({
          title: "Descarga iniciada",
          description: "Tu imagen se está descargando",
        });
      })
      .catch((error) => {
        console.error("Error descargando imagen:", error);

        // Intentar con URL absoluta si es una ruta relativa
        if (!resultImage.startsWith("http")) {
          console.log("Intentando con URL absoluta...");
          const absoluteUrl = `${window.location.origin}${resultImage}`;

          fetch(absoluteUrl)
            .then((response) => {
              if (!response.ok) {
                throw new Error(
                  `Error de red: ${response.status} ${response.statusText}`,
                );
              }
              return response.blob();
            })
            .then((blob) => {
              const blobUrl = URL.createObjectURL(blob);
              link.href = blobUrl;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              URL.revokeObjectURL(blobUrl);

              toast({
                title: "Descarga iniciada",
                description: "Tu imagen se está descargando",
              });
            })
            .catch((secondError) => {
              console.error("Error con URL absoluta:", secondError);

              toast({
                title: "Error al descargar",
                description:
                  "No se pudo descargar la imagen. Intenta guardarla manualmente con clic derecho → Guardar imagen como...",
                variant: "destructive",
              });
            });
        } else {
          toast({
            title: "Error al descargar",
            description:
              "No se pudo descargar la imagen. Intenta guardarla manualmente con clic derecho → Guardar imagen como...",
            variant: "destructive",
          });
        }
      });
  };

  // Función para copiar imagen al portapapeles
  const copyToClipboard = useCallback(async () => {
    if (!resultImage) return;

    try {
      // Convertir data URL a blob
      const response = await fetch(resultImage);
      const blob = await response.blob();

      // Copiar al portapapeles
      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);

      toast({
        title: "📋 ¡Copiado!",
        description: "Imagen copiada al portapapeles exitosamente.",
      });
    } catch (error) {
      console.error('Error al copiar al portapapeles:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo copiar la imagen al portapapeles.",
        variant: "destructive",
      });
    }
  }, [resultImage, toast]);

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!resultImage || !imageSrc) return;

    try {
      if (currentSketchSaved) {
        // Quitar de favoritos
        const savedSketch = savedSketches.find(sketch => sketch.processedUrl === resultImage);
        if (savedSketch) {
          const filteredSketches = savedSketches.filter(sketch => sketch.id !== savedSketch.id);
          setSavedSketches(filteredSketches);
          setCurrentSketchSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const sketchData = {
          originalUrl: imageSrc,
          processedUrl: resultImage,
          originalFilename: originalName || "boceto",
          prompt: prompt,
          negativePrompt: negativePrompt,
          controlStrength: controlStrength,
          stylePreset: stylePreset,
          outputFormat: outputFormat,
        };

        const newSketch = createSavedSketch(sketchData);
        const updatedSketches = [newSketch, ...savedSketches].slice(0, 50); // Limitar a 50

        setSavedSketches(updatedSketches);
        setCurrentSketchSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: "Imagen guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [resultImage, imageSrc, originalName, prompt, negativePrompt, controlStrength, stylePreset, outputFormat, currentSketchSaved, savedSketches, setSavedSketches, toast]);

  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (resultImage) {
      setCurrentSketchSaved(isSketchSaved(resultImage, savedSketches));
    }
  }, [resultImage, savedSketches]);

  return (
    <DashboardLayout pageTitle="Boceto a Imagen">
      <TooltipProvider>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                    <PenTool className="h-8 w-8 text-white" />
                  </div>
                  <h1 className="text-4xl font-bold text-white">
                    Boceto a Imagen
                  </h1>
                </div>
                <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                  Convierte tus bocetos en imágenes realistas con IA avanzada. Dibuja o sube un boceto y obtén resultados profesionales.
                </p>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Sparkles className="w-3 h-3 mr-1" />
                    IA avanzada
                  </Badge>
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Palette className="w-3 h-3 mr-1" />
                    Control creativo
                  </Badge>
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Wand2 className="w-3 h-3 mr-1" />
                    Resultados profesionales
                  </Badge>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Tabs para organizar contenido */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="max-w-7xl mx-auto">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="latest">Última Generación</TabsTrigger>
              <TabsTrigger value="saved">
                Guardados ({savedSketches.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="latest" className="mt-8">
              {/* Grid principal */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              >
                {/* Panel de Control */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  className="lg:col-span-1"
                >
                  <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-xl">
                        <Settings className="h-5 w-5 text-purple-600" />
                        Panel de Control
                      </CardTitle>
                      <CardDescription>
                        Configura tu boceto y parámetros de generación
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Subida de imagen */}
                      <div className="space-y-4">
                        <Label className="text-sm font-medium">Subir Boceto</Label>
                        {imageSrc ? (
                          <div className="relative">
                            <img
                              src={imageSrc}
                              alt="Boceto subido"
                              className="w-full max-h-48 object-contain rounded-lg border"
                            />
                            <Button
                              variant="destructive"
                              size="sm"
                              className="absolute top-2 right-2"
                              onClick={() => {
                                setImageFile(null);
                                setImageSrc(null);
                                setOriginalName("");
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <div
                            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-purple-300 hover:bg-purple-50/50 transition-colors"
                            onClick={() => document.getElementById("image-upload")?.click()}
                          >
                            <Upload className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground mb-1">
                              Haz clic para subir una imagen
                            </p>
                            <p className="text-xs text-muted-foreground">
                              JPG, PNG, WebP (máx. 10MB)
                            </p>
                            <Input
                              id="image-upload"
                              type="file"
                              accept="image/jpeg,image/png,image/webp"
                              className="hidden"
                              onChange={handleImageUpload}
                            />
                          </div>
                        )}
                      </div>
                      {/* Separador */}
                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t" />
                        </div>
                        <div className="relative flex justify-center text-xs uppercase">
                          <span className="bg-background px-2 text-muted-foreground">
                            O dibujar boceto
                          </span>
                        </div>
                      </div>

                      {/* Canvas de dibujo */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">Dibujar boceto</Label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={clearCanvas}
                            className="gap-2"
                          >
                            <Trash2 className="h-3 w-3" />
                            Limpiar
                          </Button>
                        </div>
                        <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 bg-muted/10">
                          <canvas
                            ref={canvasRef}
                            width={300}
                            height={200}
                            style={canvasStyles}
                            onMouseDown={handleMouseDown}
                            onMouseMove={handleMouseMove}
                            onMouseUp={handleMouseUp}
                            onMouseLeave={handleMouseLeave}
                            onTouchStart={handleTouchStart}
                            onTouchMove={handleTouchMove}
                            onTouchEnd={handleTouchEnd}
                            className="w-full rounded-md"
                          />
                        </div>
                        <p className="text-xs text-muted-foreground text-center">
                          Dibuja directamente en el canvas
                        </p>
                      </div>
                      {/* Prompt */}
                      <div className="space-y-2">
                        <Label htmlFor="prompt">Descripción (requerido)</Label>
                        <Textarea
                          id="prompt"
                          placeholder="Describe la imagen que quieres generar..."
                          value={prompt}
                          onChange={(e) => setPrompt(e.target.value)}
                          rows={3}
                          className="resize-none"
                        />
                      </div>

                      {/* Prompt negativo */}
                      <div className="space-y-2">
                        <Label htmlFor="negative-prompt">Prompt Negativo (opcional)</Label>
                        <Textarea
                          id="negative-prompt"
                          placeholder="Describe lo que NO quieres en la imagen..."
                          value={negativePrompt}
                          onChange={(e) => setNegativePrompt(e.target.value)}
                          rows={2}
                          className="resize-none"
                        />
                      </div>

                      {/* Control Strength */}
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label>Fuerza del Control</Label>
                          <Badge variant="outline">{controlStrength.toFixed(1)}</Badge>
                        </div>
                        <Slider
                          value={[controlStrength]}
                          min={0.1}
                          max={1}
                          step={0.1}
                          onValueChange={(value) => setControlStrength(value[0])}
                          className="w-full"
                        />
                        <p className="text-xs text-muted-foreground">
                          Valores bajos: más libertad creativa • Valores altos: sigue fielmente el boceto
                        </p>
                      </div>

                      {/* Estilo y formato */}
                      <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2">
                          <Label>Estilo</Label>
                          <Select value={stylePreset} onValueChange={setStylePreset}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {stylePresets.map((style) => (
                                <SelectItem key={style.value} value={style.value}>
                                  {style.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Formato</Label>
                          <Select value={outputFormat} onValueChange={setOutputFormat}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {outputFormats.map((format) => (
                                <SelectItem key={format.value} value={format.value}>
                                  {format.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* Botón de generar */}
                      <Button
                        className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                        onClick={generateImage}
                        disabled={!imageFile || !prompt || isGenerating}
                        size="lg"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Generando imagen...
                          </>
                        ) : (
                          <>
                            <Wand2 className="mr-2 h-4 w-4" />
                            Generar Imagen
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Área de Visualización */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="lg:col-span-2"
                >

                  {resultImage ? (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Sparkles className="h-5 w-5 text-purple-600" />
                          Imagen Generada
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Imagen resultado */}
                        <div className="flex justify-center">
                          <div className="relative">
                            <img
                              src={resultImage}
                              alt="Imagen generada"
                              className="w-full max-w-lg rounded-lg border shadow-lg"
                            />
                          </div>
                        </div>

                        {/* Botones de acción */}
                        <div className="flex flex-wrap gap-3 justify-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={copyToClipboard}
                            className="flex-1 min-w-[120px]"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copiar
                          </Button>

                          <Button
                            onClick={handleToggleFavorite}
                            className={`flex-1 min-w-[120px] ${
                              currentSketchSaved
                                ? "bg-red-500 hover:bg-red-600 text-white"
                                : "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                            }`}
                          >
                            <Heart className={`w-4 h-4 mr-2 ${currentSketchSaved ? "fill-current" : ""}`} />
                            {currentSketchSaved ? "Quitar" : "Guardar"}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={downloadResult}
                            className="flex-1 min-w-[120px]"
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Descargar
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                          <PenTool className="h-12 w-12 text-purple-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          Convierte tu boceto en imagen
                        </h3>
                        <p className="text-gray-600 mb-4 max-w-md">
                          Sube un boceto o dibuja directamente, añade una descripción y genera una imagen realista con IA.
                        </p>
                        <div className="flex items-center gap-2 text-sm text-purple-600">
                          <Sparkles className="h-4 w-4" />
                          <span>Resultados en segundos</span>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </motion.div>
              </motion.div>
            </TabsContent>

            <TabsContent value="saved" className="mt-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="h-5 w-5 text-primary" />
                    Bocetos Guardados
                    <Badge variant="secondary">{savedSketches.length}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {savedSketches.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {savedSketches.map((savedSketch) => (
                        <motion.div
                          key={savedSketch.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="bg-white border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                        >
                          {/* Imagen procesada */}
                          <div className="relative aspect-square">
                            <img
                              src={savedSketch.processedUrl}
                              alt="Boceto guardado"
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute top-2 right-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const filteredSketches = savedSketches.filter(sketch => sketch.id !== savedSketch.id);
                                  setSavedSketches(filteredSketches);

                                  toast({
                                    title: "💔 Eliminada",
                                    description: "Boceto eliminado de favoritos.",
                                  });
                                }}
                                className="bg-white/90 hover:bg-white"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          {/* Información */}
                          <div className="p-4">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="secondary" className="text-xs">
                                {savedSketch.outputFormat.toUpperCase()}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {new Date(savedSketch.timestamp).toLocaleDateString()}
                              </span>
                            </div>

                            <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                              {savedSketch.prompt}
                            </p>

                            <p className="text-xs text-muted-foreground mb-3 truncate">
                              {savedSketch.originalFilename}
                            </p>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  // Cargar el boceto en la galería principal
                                  setImageFile(null);
                                  setImageSrc(savedSketch.originalUrl);
                                  setResultImage(savedSketch.processedUrl);
                                  setOriginalName(savedSketch.originalFilename);
                                  setPrompt(savedSketch.prompt);
                                  setNegativePrompt(savedSketch.negativePrompt || "");
                                  setControlStrength(savedSketch.controlStrength);
                                  setStylePreset(savedSketch.stylePreset || "enhance");
                                  setOutputFormat(savedSketch.outputFormat);

                                  // Cambiar a la pestaña "Última Generación"
                                  setActiveTab("latest");

                                  toast({
                                    title: "🖼️ Boceto cargado",
                                    description: "Boceto cargado en la galería principal.",
                                  });
                                }}
                                className="flex-1"
                              >
                                <RotateCcw className="h-3 w-3 mr-1" />
                                Cargar
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const link = document.createElement("a");
                                  link.href = savedSketch.processedUrl;
                                  link.download = `sketch_${savedSketch.originalFilename}`;
                                  link.click();
                                }}
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-600 mb-2">
                        No hay bocetos guardados
                      </h3>
                      <p className="text-gray-500">
                        Los bocetos que guardes aparecerán aquí
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </TooltipProvider>
    </DashboardLayout>
  );
}
