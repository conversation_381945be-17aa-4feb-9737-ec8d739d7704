import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { DashboardLayout } from '../components/layout/dashboard-layout';
import {
  Megaphone,
  Camera,
  Target,
  Edit3,
  Sparkles,
  RefreshCw,
  Download,
  Copy,
  Heart,
  Share2,
  Wand2,
  <PERSON><PERSON>,
  Zap
} from 'lucide-react';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Textarea } from '../components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { useToast } from '../hooks/use-toast';

const AD_EXAMPLES = [
  {
    title: "Anuncio de Producto Tech",
    prompt: "Smartphone moderno sobre superficie minimalista con iluminación profesional, fondo degradado azul, composición comercial limpia",
    category: "Tecnología"
  },
  {
    title: "Anuncio de <PERSON>da",
    prompt: "Modelo elegante con ropa de diseñador, iluminación de estudio, fondo neutro, composición de revista de moda",
    category: "Moda"
  },
  {
    title: "Anuncio de Comida",
    prompt: "Plato gourmet con presentación profesional, iluminación cálida, ingredientes frescos, composición apetitosa",
    category: "Gastronomía"
  },
  {
    title: "Anuncio de Automóvil",
    prompt: "Auto deportivo en carretera escénica, iluminación dorada, composición dinámica, calidad cinematográfica",
    category: "Automotriz"
  }
];

const AD_SIZES = [
  { value: "1024x1024", label: "Cuadrado (1024x1024)" },
  { value: "1024x1536", label: "Vertical (1024x1536)" },
  { value: "1536x1024", label: "Horizontal (1536x1024)" }
];

export default function AdCreatorPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('generate');
  const [prompt, setPrompt] = useState('');
  const [selectedSize, setSelectedSize] = useState('1024x1024');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [savedImages, setSavedImages] = useState<string[]>([]);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Error",
        description: "Por favor, describe el anuncio que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Simular generación de anuncio
      await new Promise(resolve => setTimeout(resolve, 3000));

      const newImage = `https://picsum.photos/1024/1024?random=${Date.now()}`;
      setGeneratedImages([newImage, ...generatedImages]);
      toast({
        title: "¡Éxito!",
        description: "Anuncio generado exitosamente",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Error al generar el anuncio",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveImage = (imageUrl: string) => {
    setSavedImages([imageUrl, ...savedImages]);
    toast({
      title: "Guardado",
      description: "Anuncio guardado en favoritos",
    });
  };

  const handleDownload = (imageUrl: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `anuncio-${Date.now()}.jpg`;
    link.click();
    toast({
      title: "Descargado",
      description: "Anuncio descargado exitosamente",
    });
  };

  const handleCopyToClipboard = async (imageUrl: string) => {
    try {
      await navigator.clipboard.writeText(imageUrl);
      toast({
        title: "Copiado",
        description: "Enlace copiado al portapapeles",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Error al copiar el enlace",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Megaphone className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Crear Anuncio
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Crea anuncios profesionales con calidad de agencia. Product placement perfecto, iluminación profesional y composición comercial.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Camera className="w-3 h-3 mr-1" />
                  Calidad de agencia
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Target className="w-3 h-3 mr-1" />
                  Product placement
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Edición profesional
                </Badge>
              </div>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg"
              >
                <Sparkles className="w-4 h-4 text-purple-600" />
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Wand2 className="h-4 w-4" />
              Generar
            </TabsTrigger>
            <TabsTrigger value="examples" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Ejemplos
            </TabsTrigger>
            <TabsTrigger value="saved" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Guardados
            </TabsTrigger>
          </TabsList>

          {/* Tab Content - Generar */}
          <TabsContent value="generate" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-8"
            >
              {/* Panel de Generación */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Wand2 className="h-5 w-5 text-purple-600" />
                    Crear Anuncio
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Describe tu anuncio
                      </label>
                      <Textarea
                        placeholder="Ej: Smartphone moderno con iluminación profesional, fondo minimalista, composición comercial elegante..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[120px]"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Tamaño del anuncio
                      </label>
                      <Select value={selectedSize} onValueChange={setSelectedSize}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AD_SIZES.map((size) => (
                            <SelectItem key={size.value} value={size.value}>
                              {size.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando anuncio...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          Generar Anuncio
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Panel de Resultados */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Última Generación</h3>

                  {generatedImages.length > 0 ? (
                    <div className="space-y-4">
                      <div className="relative group">
                        <img
                          src={generatedImages[0]}
                          alt="Anuncio generado"
                          className="w-full rounded-lg shadow-lg"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleSaveImage(generatedImages[0])}
                          >
                            <Heart className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleDownload(generatedImages[0])}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleCopyToClipboard(generatedImages[0])}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12 text-gray-500">
                      <Megaphone className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Genera tu primer anuncio profesional</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Tab Content - Ejemplos */}
          <TabsContent value="examples" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 gap-6"
            >
              {AD_EXAMPLES.map((example, index) => (
                <Card key={index} className="cursor-pointer hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-semibold text-lg">{example.title}</h3>
                      <Badge variant="secondary">{example.category}</Badge>
                    </div>
                    <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                      {example.prompt}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setPrompt(example.prompt);
                        setActiveTab('generate');
                      }}
                      className="w-full"
                    >
                      <Wand2 className="h-4 w-4 mr-2" />
                      Usar este ejemplo
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </motion.div>
          </TabsContent>

          {/* Tab Content - Guardados */}
          <TabsContent value="saved" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              {savedImages.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {savedImages.map((imageUrl, index) => (
                    <Card key={index} className="group">
                      <CardContent className="p-4">
                        <div className="relative">
                          <img
                            src={imageUrl}
                            alt={`Anuncio guardado ${index + 1}`}
                            className="w-full rounded-lg shadow-md"
                          />
                          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => handleDownload(imageUrl)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => handleCopyToClipboard(imageUrl)}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Heart className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-semibold mb-2">No hay anuncios guardados</h3>
                    <p className="text-gray-600 mb-4">
                      Guarda tus anuncios favoritos para acceder a ellos fácilmente
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('generate')}
                    >
                      <Wand2 className="h-4 w-4 mr-2" />
                      Crear primer anuncio
                    </Button>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}