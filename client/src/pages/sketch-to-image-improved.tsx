"use client";

import React, { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { useLocation } from "wouter";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import {
  Upload,
  Wand2,
  Download,
  ImagePlus,
  Loader2,
  CheckCircle,
  Sparkles,
  Copy,
  Heart,
  Trash2,
  RotateCcw,
  Palette,
  PenTool,
  Settings,
  Info,
  ChevronDown,
  ChevronUp,
  Crown,
  Zap,
  Cpu,
  Images,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { convertSketchToImage } from "@/services/sketch-to-image-service";

// Interfaz para imágenes guardadas
interface SavedSketch {
  id: string;
  originalUrl: string;
  processedUrl: string;
  originalFilename: string;
  prompt: string;
  negativePrompt?: string;
  controlStrength: number;
  stylePreset?: string;
  outputFormat: string;
  timestamp: number;
  isFavorite: boolean;
}

// Custom hook para localStorage
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Funciones para manejar imágenes guardadas
const SAVED_SKETCHES_KEY = 'emma_saved_sketch_to_image';

const createSavedSketch = (sketchData: Omit<SavedSketch, 'id' | 'timestamp' | 'isFavorite'>): SavedSketch => {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 1000000).toString();
  return {
    ...sketchData,
    id: `sketch_${timestamp}_${randomPart}`,
    timestamp: timestamp,
    isFavorite: true,
  };
};

const isSketchSaved = (imageUrl: string, savedSketches: SavedSketch[]): boolean => {
  return savedSketches.some(sketch => sketch.processedUrl === imageUrl);
};

// Estilos para el canvas
const canvasStyles: React.CSSProperties = {
  border: "3px solid #000",
  borderRadius: "12px",
  cursor: "crosshair",
  backgroundColor: "white",
  boxShadow: "4px 4px 0px 0px rgba(0,0,0,1)",
};

// Opciones de estilo mejoradas con emojis
const stylePresets = [
  { value: "enhance", label: "✨ Mejorar", description: "Calidad mejorada" },
  { value: "3d-model", label: "🎯 Modelo 3D", description: "Renderizado 3D" },
  { value: "analog-film", label: "📷 Película Analógica", description: "Fotografía vintage" },
  { value: "anime", label: "🌸 Anime", description: "Estilo anime japonés" },
  { value: "cinematic", label: "🎬 Cinematográfico", description: "Estilo de película" },
  { value: "comic-book", label: "💥 Cómic", description: "Estilo de cómic" },
  { value: "digital-art", label: "🎨 Arte Digital", description: "Arte digital moderno" },
  { value: "fantasy-art", label: "🧙 Arte Fantástico", description: "Fantasía épica" },
  { value: "isometric", label: "📐 Isométrico", description: "Vista isométrica" },
  { value: "line-art", label: "✏️ Arte Lineal", description: "Dibujo a líneas" },
  { value: "low-poly", label: "🔷 Low Poly", description: "Polígonos bajos" },
  { value: "neon-punk", label: "⚡ Neon Punk", description: "Cyberpunk neón" },
  { value: "origami", label: "🗾 Origami", description: "Papel plegado" },
  { value: "photographic", label: "📸 Fotográfico", description: "Realismo fotográfico" },
  { value: "pixel-art", label: "🕹️ Pixel Art", description: "Arte de píxeles retro" },
  { value: "tile-texture", label: "🧱 Textura", description: "Texturas repetibles" },
];

const outputFormats = [
  { value: "png", label: "PNG", description: "Sin pérdida, transparencia" },
  { value: "jpeg", label: "JPEG", description: "Compatibilidad universal" },
  { value: "webp", label: "WebP", description: "Mejor compresión (recomendado)" },
];



export default function SketchToImagePage() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();

  // Estado para la imagen
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [originalName, setOriginalName] = useState<string>("");

  // Estados para las opciones
  const [prompt, setPrompt] = useState<string>("");
  const [negativePrompt, setNegativePrompt] = useState<string>("");
  const [controlStrength, setControlStrength] = useState<number>(0.7);
  const [stylePreset, setStylePreset] = useState<string>("enhance");
  const [outputFormat, setOutputFormat] = useState<string>("webp");

  // Estados de UI
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Referencias para el canvas
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<CanvasRenderingContext2D | null>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);

  // Estados para la funcionalidad de guardados
  const [savedSketches, setSavedSketches] = useLocalStorage<SavedSketch[]>(SAVED_SKETCHES_KEY, []);
  const [currentSketchSaved, setCurrentSketchSaved] = useState(false);
  const [activeTab, setActiveTab] = useState("latest");

  // Inicializar el canvas
  const initCanvas = (canvas: HTMLCanvasElement) => {
    const context = canvas.getContext("2d");
    if (context) {
      context.lineCap = "round";
      context.strokeStyle = "black";
      context.lineWidth = 3;
      contextRef.current = context;
      context.fillStyle = "white";
      context.fillRect(0, 0, canvas.width, canvas.height);
    }
  };

  React.useEffect(() => {
    if (canvasRef.current) {
      initCanvas(canvasRef.current);
    }
  }, []);

  // Funciones para dibujar
  const startDrawing = useCallback((x: number, y: number) => {
    if (!contextRef.current) return;
    contextRef.current.beginPath();
    contextRef.current.moveTo(x, y);
    setIsDrawing(true);
  }, []);

  const draw = useCallback((x: number, y: number) => {
    if (!contextRef.current || !isDrawing) return;
    contextRef.current.lineTo(x, y);
    contextRef.current.stroke();
  }, [isDrawing]);

  const stopDrawing = useCallback(() => {
    if (!contextRef.current) return;
    contextRef.current.closePath();
    setIsDrawing(false);

    if (canvasRef.current) {
      canvasRef.current.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], "sketch.png", { type: "image/png" });
          setImageFile(file);
          setImageSrc(URL.createObjectURL(blob));
          setOriginalName("sketch.png");
        }
      }, "image/png");
    }
  }, []);

  // Manejadores de eventos del canvas
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    startDrawing(x, y);
  }, [startDrawing]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !isDrawing) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    draw(x, y);
  }, [draw, isDrawing]);

  const handleMouseUp = useCallback(() => {
    stopDrawing();
  }, [stopDrawing]);

  const handleMouseLeave = useCallback(() => {
    stopDrawing();
  }, [stopDrawing]);

  // Manejadores táctiles
  const handleTouchStart = useCallback((e: React.TouchEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;
    e.preventDefault();
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.touches[0].clientX - rect.left;
    const y = e.touches[0].clientY - rect.top;
    startDrawing(x, y);
  }, [startDrawing]);

  const handleTouchMove = useCallback((e: React.TouchEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !isDrawing) return;
    e.preventDefault();
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.touches[0].clientX - rect.left;
    const y = e.touches[0].clientY - rect.top;
    draw(x, y);
  }, [draw, isDrawing]);

  const handleTouchEnd = useCallback(() => {
    stopDrawing();
  }, [stopDrawing]);

  // Limpiar canvas
  const clearCanvas = useCallback(() => {
    if (!canvasRef.current || !contextRef.current) return;
    contextRef.current.fillStyle = "white";
    contextRef.current.fillRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    setImageFile(null);
    setImageSrc(null);
    setOriginalName("");
  }, []);

  // Subir imagen
  const handleImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const allowedTypes = ["image/jpeg", "image/png", "image/webp"];

      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "❌ Formato incorrecto",
          description: "Solo se permiten imágenes en formato JPG, PNG o WebP.",
          variant: "destructive",
        });
        return;
      }

      const MAX_SIZE = 10 * 1024 * 1024;
      if (file.size > MAX_SIZE) {
        toast({
          title: "❌ Archivo demasiado grande",
          description: "La imagen excede el límite de 10MB.",
          variant: "destructive",
        });
        return;
      }

      setImageFile(file);
      setImageSrc(URL.createObjectURL(file));
      setOriginalName(file.name);

      toast({
        title: "✅ Imagen cargada",
        description: "Tu imagen se ha cargado correctamente.",
      });
    }
  }, [toast]);

  // Generar imagen
  const generateImage = async () => {
    if (!imageFile) {
      toast({
        title: "❌ Error",
        description: "Primero dibuja o sube una imagen",
        variant: "destructive",
      });
      return;
    }

    if (!prompt) {
      toast({
        title: "❌ Error",
        description: "El prompt es obligatorio",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const response = await convertSketchToImage(imageFile, {
        prompt,
        negativePrompt: negativePrompt || undefined,
        controlStrength,
        seed: Math.floor(Math.random() * 1000000), // Siempre aleatorio
        outputFormat: outputFormat as "jpeg" | "png" | "webp",
        stylePreset: stylePreset as any,
      });

      if (response.success && response.imageUrl) {
        setResultImage(response.imageUrl);
        toast({
          title: "🎉 ¡Imagen generada!",
          description: "Tu boceto se ha convertido en una imagen increíble.",
        });
      } else {
        toast({
          title: "❌ Error",
          description: response.error || "Error al generar la imagen",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error al generar imagen:", error);
      let errorMessage = "Ocurrió un error al procesar tu solicitud";

      if (error.response) {
        const status = error.response.status;
        if (status === 500) {
          errorMessage = "Error en el servidor. Verifica el formato de imagen y el tamaño.";
        } else if (status === 413) {
          errorMessage = "La imagen es demasiado grande. El límite es de 10MB.";
        } else if (status === 400) {
          errorMessage = "Parámetros incorrectos. Verifica tus opciones.";
        }
      }

      toast({
        title: "❌ Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };





  // Resetear formulario
  const resetForm = () => {
    setPrompt("");
    setNegativePrompt("");
    setControlStrength(0.7);
    setStylePreset("enhance");
    setOutputFormat("webp");
    setShowAdvanced(false);
    clearCanvas();

    toast({
      title: "🔄 Formulario limpiado",
      description: "Todos los campos han sido restablecidos",
    });
  };

  // Copiar al portapapeles
  const copyToClipboard = useCallback(async () => {
    if (!resultImage) return;

    try {
      const response = await fetch(resultImage);
      const blob = await response.blob();

      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);

      toast({
        title: "📋 ¡Copiado!",
        description: "Imagen copiada al portapapeles exitosamente.",
      });
    } catch (error) {
      console.error('Error al copiar al portapapeles:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo copiar la imagen al portapapeles.",
        variant: "destructive",
      });
    }
  }, [resultImage, toast]);

  // Manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!resultImage || !imageSrc) return;

    try {
      if (currentSketchSaved) {
        const savedSketch = savedSketches.find(sketch => sketch.processedUrl === resultImage);
        if (savedSketch) {
          const filteredSketches = savedSketches.filter(sketch => sketch.id !== savedSketch.id);
          setSavedSketches(filteredSketches);
          setCurrentSketchSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        const sketchData = {
          originalUrl: imageSrc,
          processedUrl: resultImage,
          originalFilename: originalName || "boceto",
          prompt: prompt,
          negativePrompt: negativePrompt,
          controlStrength: controlStrength,
          stylePreset: stylePreset,
          outputFormat: outputFormat,
        };

        const newSketch = createSavedSketch(sketchData);
        const updatedSketches = [newSketch, ...savedSketches].slice(0, 50);

        setSavedSketches(updatedSketches);
        setCurrentSketchSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: "Imagen guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [resultImage, imageSrc, originalName, prompt, negativePrompt, controlStrength, stylePreset, outputFormat, currentSketchSaved, savedSketches, setSavedSketches, toast]);

  // Descargar imagen
  const downloadResult = () => {
    if (!resultImage) return;

    const link = document.createElement("a");
    link.href = resultImage;
    link.download = `sketch-to-image.${outputFormat}`;
    link.click();

    toast({
      title: "📥 Descarga iniciada",
      description: "Tu imagen se está descargando.",
    });
  };

  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (resultImage) {
      setCurrentSketchSaved(isSketchSaved(resultImage, savedSketches));
    }
  }, [resultImage, savedSketches]);

  return (
    <TooltipProvider>
      <DashboardLayout pageTitle="Boceto a Imagen IA">
        <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
          {/* Header con gradiente */}
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 text-white py-16 overflow-hidden">
            {/* Elementos decorativos de fondo */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute bg-white/10 rounded-full"
                  style={{
                    width: `${Math.random() * 100 + 20}px`,
                    height: `${Math.random() * 100 + 20}px`,
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    x: [0, Math.random() * 100 - 50],
                    y: [0, Math.random() * 100 - 50],
                    scale: [1, 1.2, 0.8, 1],
                    opacity: [0.3, 0.6, 0.3],
                  }}
                  transition={{
                    duration: 8 + Math.random() * 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              ))}
            </div>

            <div className="container mx-auto px-4 relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <PenTool className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Boceto a Imagen IA
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Convierte tus bocetos y dibujos en imágenes realistas con IA avanzada.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Canvas integrado
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <PenTool className="w-3 h-3 mr-1" />
                  Dibujo directo
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Wand2 className="w-3 h-3 mr-1" />
                  IA avanzada
                </Badge>
              </div>
            </div>
          </div>

          {/* Contenido principal */}
          <div className="container mx-auto px-4 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Panel de Control */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="lg:col-span-1"
              >
                <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <Settings className="h-5 w-5 text-purple-600" />
                      Panel de Control
                    </CardTitle>
                    <CardDescription>
                      Dibuja o sube tu boceto y configura la generación
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">

                    {/* Canvas de dibujo */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <Label className="text-sm font-medium">🖌️ Canvas de Dibujo</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={clearCanvas}
                          className="h-6 px-2 text-xs"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Limpiar
                        </Button>
                      </div>
                      <div className="border-2 border-gray-300 rounded-lg p-2 bg-gray-50">
                        <canvas
                          ref={canvasRef}
                          width={300}
                          height={200}
                          style={canvasStyles}
                          onMouseDown={handleMouseDown}
                          onMouseMove={handleMouseMove}
                          onMouseUp={handleMouseUp}
                          onMouseLeave={handleMouseLeave}
                          onTouchStart={handleTouchStart}
                          onTouchMove={handleTouchMove}
                          onTouchEnd={handleTouchEnd}
                          className="w-full rounded-lg"
                        />
                      </div>
                      <p className="text-xs text-gray-600 text-center">
                        Dibuja directamente en el canvas
                      </p>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-2">
                        <div className="flex items-start gap-2">
                          <div className="text-blue-500 mt-0.5">💡</div>
                          <p className="text-xs text-blue-700 font-medium">
                            <strong>Consejo:</strong> Entre más detallado sea tu sketch, mejor actuará la IA sobre esa imagen. Incluye líneas claras y formas definidas.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Separador */}
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-gray-300" />
                      </div>
                      <div className="relative flex justify-center text-xs">
                        <span className="bg-white px-2 text-gray-500">
                          O subir imagen
                        </span>
                      </div>
                    </div>

                    {/* Subida de imagen */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">📁 Subir Boceto</Label>
                      {imageSrc ? (
                        <div className="relative border-2 border-gray-300 rounded-lg overflow-hidden">
                          <img
                            src={imageSrc}
                            alt="Boceto subido"
                            className="w-full h-32 object-cover bg-white"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute top-1 right-1 h-6 w-6 p-0"
                            onClick={() => {
                              setImageFile(null);
                              setImageSrc(null);
                              setOriginalName("");
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <div
                          className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                          onClick={() => document.getElementById("image-upload")?.click()}
                        >
                          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600 mb-1">
                            Haz clic para subir
                          </p>
                          <p className="text-xs text-gray-500">
                            JPG, PNG, WebP (máx. 10MB)
                          </p>
                          <Input
                            id="image-upload"
                            type="file"
                            accept="image/jpeg,image/png,image/webp"
                            className="hidden"
                            onChange={handleImageUpload}
                          />
                        </div>
                      )}
                    </div>

                    {/* Campo de Prompt */}
                    <div className="space-y-3">
                      <Label htmlFor="prompt" className="text-sm font-medium">✨ Descripción de la imagen</Label>
                      <Textarea
                        id="prompt"
                        placeholder="Describe con detalle lo que quieres ver en la imagen..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    {/* Estilo visual */}
                    <div className="space-y-2">
                      <Label htmlFor="style-preset" className="text-sm font-medium">🎨 Estilo</Label>
                      <Select value={stylePreset} onValueChange={setStylePreset}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un estilo" />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {stylePresets.map((style) => (
                            <SelectItem key={style.value} value={style.value}>
                              <div className="flex flex-col">
                                <span className="font-medium">{style.label}</span>
                                <span className="text-xs text-gray-500">{style.description}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Formato de salida */}
                    <div className="space-y-2">
                      <Label htmlFor="output-format" className="text-sm font-medium">💾 Formato</Label>
                      <Select value={outputFormat} onValueChange={setOutputFormat}>
                        <SelectTrigger>
                          <SelectValue placeholder="Formato de salida" />
                        </SelectTrigger>
                        <SelectContent>
                          {outputFormats.map((format) => (
                            <SelectItem key={format.value} value={format.value}>
                              <div className="flex flex-col">
                                <span className="font-medium">{format.label}</span>
                                <span className="text-xs text-gray-500">{format.description}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Control Strength */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <Label className="text-sm font-medium">⚡ Fuerza del Control</Label>
                        <span className="text-sm text-gray-500">{controlStrength.toFixed(1)}</span>
                      </div>
                      <Slider
                        value={[controlStrength]}
                        min={0.1}
                        max={1}
                        step={0.1}
                        onValueChange={(value) => setControlStrength(value[0])}
                        className="w-full"
                      />
                      <p className="text-xs text-gray-600">
                        💡 Valores bajos: más libertad creativa • Valores altos: sigue fielmente el boceto
                      </p>
                    </div>

                      {/* Opciones avanzadas - Collapsible Neo-Brutal */}
                      <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
                        <CollapsibleTrigger asChild>
                          <Button
                            type="button"
                            variant="outline"
                            className="w-full"
                          >
                            <Settings className="w-4 h-4 mr-2" />
                            Opciones Avanzadas
                            {showAdvanced ? <ChevronUp className="w-4 h-4 ml-2" /> : <ChevronDown className="w-4 h-4 ml-2" />}
                          </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="space-y-4 pt-4">
                          <AnimatePresence>
                            {showAdvanced && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                className="space-y-4 border-t border-gray-200 pt-4"
                              >
                                {/* Negative Prompt */}
                                <div className="space-y-2">
                                  <Label htmlFor="negative-prompt" className="text-sm font-medium flex items-center gap-2">
                                    🚫 Elementos a evitar
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Info className="w-4 h-4 text-gray-400" />
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>Especifica qué NO quieres ver en la imagen</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </Label>
                                  <Textarea
                                    id="negative-prompt"
                                    placeholder="borroso, baja calidad, distorsionado, feo, manos deformadas, texto, marca de agua..."
                                    value={negativePrompt}
                                    onChange={(e) => setNegativePrompt(e.target.value)}
                                    className="min-h-[60px] resize-none"
                                  />
                                </div>


                              </motion.div>
                            )}
                          </AnimatePresence>
                        </CollapsibleContent>
                      </Collapsible>
                    {/* Botones de acción */}
                    <div className="space-y-3">
                      <Button
                        onClick={generateImage}
                        disabled={isGenerating || !imageFile || !prompt.trim()}
                        className="w-full h-12 text-lg bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                        size="lg"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                            Generando imagen...
                          </>
                        ) : (
                          <>
                            <Wand2 className="w-5 h-5 mr-2" />
                            Generar Imagen IA
                          </>
                        )}
                      </Button>

                      <Button
                        onClick={resetForm}
                        variant="outline"
                        className="w-full"
                        size="sm"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Limpiar Todo
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Área de Visualización */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="latest" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Última Generación
                  </TabsTrigger>
                  <TabsTrigger value="saved" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    Guardados ({savedSketches.length})
                  </TabsTrigger>
                </TabsList>

                {/* Tab: Última Generación */}
                <TabsContent value="latest" className="mt-0">
                  <AnimatePresence mode="wait">
                    {resultImage ? (
                      <motion.div
                        key="result"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                          <CardHeader className="pb-4">
                            <CardTitle className="flex items-center gap-2">
                              <Sparkles className="w-5 h-5 text-purple-600" />
                              Resultado del Boceto
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-6">
                            {/* Imagen resultado */}
                            <div className="relative group">
                              <img
                                src={resultImage}
                                alt="Imagen generada"
                                className="w-full rounded-lg shadow-lg"
                              />
                            </div>

                            {/* Información del boceto */}
                            {imageSrc && (
                              <div className="p-4 bg-gray-50 rounded-lg">
                                <h4 className="font-medium text-sm text-gray-700 mb-2">Boceto Original</h4>
                                <img
                                  src={imageSrc}
                                  alt="Boceto original"
                                  className="w-full h-24 object-cover rounded-md"
                                />
                                <p className="text-xs text-gray-500 mt-1">{originalName}</p>
                              </div>
                            )}

                            {/* Configuración utilizada */}
                            <div className="grid grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Estilo</p>
                                <p className="font-medium text-sm">{stylePreset}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Fuerza</p>
                                <p className="font-medium text-sm">{controlStrength.toFixed(1)}</p>
                              </div>
                            </div>

                            {/* Botones de acción */}
                            <div className="flex flex-wrap gap-3">
                              <Button
                                onClick={downloadResult}
                                className="flex-1 min-w-[120px]"
                                variant="outline"
                              >
                                <Download className="w-4 h-4 mr-2" />
                                Descargar
                              </Button>
                              <Button
                                onClick={copyToClipboard}
                                className="flex-1 min-w-[120px]"
                                variant="outline"
                              >
                                <Copy className="w-4 h-4 mr-2" />
                                Copiar
                              </Button>
                              <Button
                                onClick={handleToggleFavorite}
                                className={`flex-1 min-w-[120px] ${
                                  currentSketchSaved
                                    ? "bg-red-500 hover:bg-red-600 text-white"
                                    : "bg-purple-500 hover:bg-purple-600 text-white"
                                }`}
                              >
                                <Heart className={`w-4 h-4 mr-2 ${currentSketchSaved ? "fill-current" : ""}`} />
                                {currentSketchSaved ? "Quitar" : "Guardar"}
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="placeholder"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="text-center py-16"
                      >
                        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                          <CardContent className="py-16">
                            <div className="space-y-4">
                              <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto">
                                <PenTool className="w-12 h-12 text-purple-600" />
                              </div>
                              <h3 className="text-xl font-semibold text-gray-700">
                                ¡Crea tu primera imagen!
                              </h3>
                              <p className="text-gray-500 max-w-md mx-auto">
                                Dibuja un boceto o sube una imagen, añade una descripción y genera una imagen increíble con IA.
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </TabsContent>

                {/* Tab: Guardados */}
                <TabsContent value="saved" className="mt-0">
                  {savedSketches.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {savedSketches.map((savedSketch) => (
                        <motion.div
                          key={savedSketch.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-shadow">
                            <CardContent className="p-4">
                              <div className="space-y-4">
                                {/* Imagen resultado */}
                                <div className="relative group">
                                  <img
                                    src={savedSketch.processedUrl}
                                    alt="Boceto guardado"
                                    className="w-full h-48 object-cover rounded-lg"
                                  />
                                </div>

                                {/* Información */}
                                <div className="space-y-2">
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <p className="text-sm font-medium text-gray-700 line-clamp-2">
                                        {savedSketch.prompt}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {new Date(savedSketch.timestamp).toLocaleDateString()}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Configuración */}
                                  <div className="grid grid-cols-2 gap-2 text-xs">
                                    <div className="text-center p-2 bg-gray-50 rounded">
                                      <p className="text-gray-600">Estilo</p>
                                      <p className="font-medium">{savedSketch.stylePreset}</p>
                                    </div>
                                    <div className="text-center p-2 bg-gray-50 rounded">
                                      <p className="text-gray-600">Fuerza</p>
                                      <p className="font-medium">{savedSketch.controlStrength.toFixed(1)}</p>
                                    </div>
                                  </div>
                                </div>

                                {/* Botones de acción */}
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      // Cargar el boceto en la vista principal
                                      setImageFile(null);
                                      setImageSrc(savedSketch.originalUrl);
                                      setResultImage(savedSketch.processedUrl);
                                      setOriginalName(savedSketch.originalFilename);
                                      setPrompt(savedSketch.prompt);
                                      setNegativePrompt(savedSketch.negativePrompt || "");
                                      setControlStrength(savedSketch.controlStrength);
                                      setStylePreset(savedSketch.stylePreset || "enhance");
                                      setOutputFormat(savedSketch.outputFormat);

                                      // Cambiar a la pestaña "Última Generación"
                                      setActiveTab("latest");

                                      toast({
                                        title: "🖼️ Boceto cargado",
                                        description: "Boceto cargado en la vista principal.",
                                      });
                                    }}
                                    className="flex-1"
                                  >
                                    <Eye className="w-3 h-3 mr-1" />
                                    Ver
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      const link = document.createElement("a");
                                      link.href = savedSketch.processedUrl;
                                      link.download = `sketch_${savedSketch.originalFilename}`;
                                      link.click();

                                      toast({
                                        title: "Descarga iniciada",
                                        description: "La imagen se está descargando",
                                      });
                                    }}
                                    className="flex-1"
                                  >
                                    <Download className="w-3 h-3 mr-1" />
                                    Descargar
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      const filteredSketches = savedSketches.filter(sketch => sketch.id !== savedSketch.id);
                                      setSavedSketches(filteredSketches);

                                      toast({
                                        title: "🗑️ Eliminado",
                                        description: "Boceto eliminado de guardados.",
                                      });
                                    }}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-16"
                    >
                      <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                        <CardContent className="py-16">
                          <div className="space-y-4">
                            <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto">
                              <Heart className="w-12 h-12 text-purple-500" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-700">
                              No hay bocetos guardados
                            </h3>
                            <p className="text-gray-500 max-w-md mx-auto">
                              Los bocetos que guardes aparecerán aquí para acceso rápido.
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )}
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        </div>
      </DashboardLayout>
    </TooltipProvider>
  );
}
