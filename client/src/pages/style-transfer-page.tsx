/**
 * Página de Transferencia de Estilo
 * Permite transferir el estilo de una imagen a otra usando Stability AI
 * Sigue el mismo patrón que poster-creator, meme-creator y ad-creator
 */

import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import {
  transferStyleWithProgress,
  StyleTransferOptions,
} from '@/services/style-transfer-service';
import { useBackgroundTasks } from '@/context/BackgroundTasksContext';
import {
  Upload,
  Image as ImageIcon,
  Wand2,
  Loader2,
  Download,
  Heart,
  Copy,
  Palette,
  Sparkles,
  Trash2,
  ArrowRight,
  Settings,
  Zap,
  Eye,
  EyeOff,
} from 'lucide-react';

// Tipos para el estado de la aplicación
interface GeneratedStyleTransfer {
  id: string;
  initImageUrl: string;
  styleImageUrl: string;
  resultImageUrl: string;
  initImageFilename: string;
  styleImageFilename: string;
  prompt?: string;
  negativePrompt?: string;
  styleStrength: number;
  compositionFidelity: number;
  changeStrength: number;
  seed?: number;
  outputFormat: string;
  timestamp: number;
}

interface SavedStyleTransfer {
  id: string;
  initImageUrl: string;
  styleImageUrl: string;
  resultImageUrl: string;
  initImageFilename: string;
  styleImageFilename: string;
  prompt?: string;
  negativePrompt?: string;
  styleStrength: number;
  compositionFidelity: number;
  changeStrength: number;
  seed?: number;
  outputFormat: string;
  timestamp: number;
}

// Constantes
const SAVED_STYLE_TRANSFERS_KEY = 'emma_saved_style_transfers';

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

export default function StyleTransferPage() {
  const { toast } = useToast();
  const { updateTask } = useBackgroundTasks();

  // Estados principales
  const [currentStyleTransfer, setCurrentStyleTransfer] = useState<GeneratedStyleTransfer | null>(null);
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para imágenes
  const [initImage, setInitImage] = useState<File | null>(null);
  const [styleImage, setStyleImage] = useState<File | null>(null);
  const [initImagePreview, setInitImagePreview] = useState<string | null>(null);
  const [styleImagePreview, setStyleImagePreview] = useState<string | null>(null);

  // Estados para configuración
  const [styleStrength, setStyleStrength] = useState([0.5]);
  const [compositionFidelity, setCompositionFidelity] = useState([0.5]);
  const [changeStrength, setChangeStrength] = useState([0.5]);
  const [outputFormat, setOutputFormat] = useState<"jpeg" | "png" | "webp">("png");

  // Estados para favoritos
  const [savedStyleTransfers, setSavedStyleTransfers] = useLocalStorage<SavedStyleTransfer[]>(SAVED_STYLE_TRANSFERS_KEY, []);
  const [currentStyleTransferSaved, setCurrentStyleTransferSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  // Referencias
  const initImageInputRef = useRef<HTMLInputElement>(null);
  const styleImageInputRef = useRef<HTMLInputElement>(null);

  // Función para validar archivos de imagen
  const validateImageFile = (file: File) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Tipo de archivo no válido. Use JPEG, PNG o WebP.' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'El archivo es muy grande. Máximo 10MB.' };
    }

    return { valid: true };
  };

  // Función para manejar la imagen inicial
  const handleInitImageChange = (file: File) => {
    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setInitImage(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setInitImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Función para manejar la imagen de estilo
  const handleStyleImageChange = (file: File) => {
    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setStyleImage(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setStyleImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Función para generar transferencia de estilo
  const handleGenerate = async () => {
    if (!initImage || !styleImage) {
      toast({
        title: "Imágenes requeridas",
        description: "Debes subir tanto la imagen original como la imagen de estilo.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setCurrentStyleTransfer(null);
    setCurrentStyleTransferSaved(false);

    try {
      const taskId = `style_transfer_${Date.now()}`;

      const options: StyleTransferOptions = {
        initImage,
        styleImage,
        prompt: prompt.trim() || undefined,
        negativePrompt: negativePrompt.trim() || undefined,
        styleStrength: styleStrength[0],
        compositionFidelity: compositionFidelity[0],
        changeStrength: changeStrength[0],
        seed: Math.floor(Math.random() * 1000000), // Siempre aleatorio
        outputFormat,
      };

      const imageUrl = await transferStyleWithProgress(
        options,
        updateTask,
        taskId,
        true // keepInOriginPage
      );

      const newStyleTransfer: GeneratedStyleTransfer = {
        id: Date.now().toString(),
        initImageUrl: initImagePreview!,
        styleImageUrl: styleImagePreview!,
        resultImageUrl: imageUrl,
        initImageFilename: initImage.name,
        styleImageFilename: styleImage.name,
        prompt: prompt.trim() || undefined,
        negativePrompt: negativePrompt.trim() || undefined,
        styleStrength: styleStrength[0],
        compositionFidelity: compositionFidelity[0],
        changeStrength: changeStrength[0],
        seed: Math.floor(Math.random() * 1000000), // Siempre aleatorio
        outputFormat,
        timestamp: Date.now(),
      };

      setCurrentStyleTransfer(newStyleTransfer);

      toast({
        title: "✅ ¡Éxito!",
        description: "Estilo transferido exitosamente.",
      });
    } catch (error) {
      console.error("Error generating style transfer:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al transferir el estilo",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para crear un objeto SavedStyleTransfer
  const createSavedStyleTransfer = (styleTransferData: GeneratedStyleTransfer): SavedStyleTransfer => {
    return {
      id: styleTransferData.id,
      initImageUrl: styleTransferData.initImageUrl,
      styleImageUrl: styleTransferData.styleImageUrl,
      resultImageUrl: styleTransferData.resultImageUrl,
      initImageFilename: styleTransferData.initImageFilename,
      styleImageFilename: styleTransferData.styleImageFilename,
      prompt: styleTransferData.prompt,
      negativePrompt: styleTransferData.negativePrompt,
      styleStrength: styleTransferData.styleStrength,
      compositionFidelity: styleTransferData.compositionFidelity,
      changeStrength: styleTransferData.changeStrength,
      seed: styleTransferData.seed,
      outputFormat: styleTransferData.outputFormat,
      timestamp: styleTransferData.timestamp,
    };
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(async () => {
    if (!currentStyleTransfer) return;

    try {
      if (currentStyleTransferSaved) {
        // Remover de favoritos
        const updatedStyleTransfers = savedStyleTransfers.filter(
          (saved) => saved.resultImageUrl !== currentStyleTransfer.resultImageUrl
        );
        setSavedStyleTransfers(updatedStyleTransfers);
        setCurrentStyleTransferSaved(false);

        toast({
          title: "💔 Removido de favoritos",
          description: "Transferencia de estilo removida de tus favoritos.",
        });
      } else {
        // Agregar a favoritos
        const styleTransferData = createSavedStyleTransfer(currentStyleTransfer);

        const newStyleTransfer = createSavedStyleTransfer(styleTransferData);
        const updatedStyleTransfers = [newStyleTransfer, ...savedStyleTransfers].slice(0, 50); // Limitar a 50

        setSavedStyleTransfers(updatedStyleTransfers);
        setCurrentStyleTransferSaved(true);

        toast({
          title: "❤️ ¡Guardado en favoritos!",
          description: "Transferencia de estilo guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar la transferencia de estilo. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [currentStyleTransfer, currentStyleTransferSaved, savedStyleTransfers, setSavedStyleTransfers, toast]);

  // Función para descargar imagen
  const handleDownload = async () => {
    if (!currentStyleTransfer?.resultImageUrl) return;

    try {
      const response = await fetch(currentStyleTransfer.resultImageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `style-transfer-${Date.now()}.${outputFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Descarga iniciada",
        description: "La imagen se está descargando",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo descargar la imagen",
        variant: "destructive",
      });
    }
  };

  // Función para copiar imagen al portapapeles
  const handleCopyToClipboard = async () => {
    if (!currentStyleTransfer?.resultImageUrl) return;

    try {
      const response = await fetch(currentStyleTransfer.resultImageUrl);
      const blob = await response.blob();

      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);

      toast({
        title: "✅ ¡Copiado!",
        description: "Imagen copiada al portapapeles exitosamente.",
      });
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo copiar la imagen al portapapeles.",
        variant: "destructive",
      });
    }
  };

  // Función para limpiar imágenes
  const handleClearImages = () => {
    setInitImage(null);
    setStyleImage(null);
    setInitImagePreview(null);
    setStyleImagePreview(null);
    setCurrentStyleTransfer(null);
    setCurrentStyleTransferSaved(false);
  };



  // Verificar si la transferencia actual está guardada
  React.useEffect(() => {
    if (currentStyleTransfer) {
      const isAlreadySaved = savedStyleTransfers.some(
        (saved) => saved.resultImageUrl === currentStyleTransfer.resultImageUrl
      );
      setCurrentStyleTransferSaved(isAlreadySaved);
    }
  }, [currentStyleTransfer, savedStyleTransfers]);

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
        {/* Header con gradiente */}
        <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 text-white py-16 overflow-hidden">
          {/* Elementos decorativos de fondo */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute bg-white/10 rounded-full"
                style={{
                  width: `${Math.random() * 100 + 20}px`,
                  height: `${Math.random() * 100 + 20}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  x: [0, Math.random() * 100 - 50],
                  y: [0, Math.random() * 100 - 50],
                  scale: [1, 1.2, 0.8, 1],
                  opacity: [0.3, 0.6, 0.3],
                }}
                transition={{
                  duration: 8 + Math.random() * 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                <Palette className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-white">
                Transferir Estilo
              </h1>
            </div>
            <p className="text-xl text-purple-100 mb-6 max-w-3xl">
              Transfiere el estilo de una imagen de referencia a tu imagen original usando IA avanzada.
            </p>
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-white/20 text-white border-white/30">
                <Sparkles className="w-3 h-3 mr-1" />
                Transferencia precisa
              </Badge>
              <Badge className="bg-white/20 text-white border-white/30">
                <ImageIcon className="w-3 h-3 mr-1" />
                Múltiples formatos
              </Badge>
              <Badge className="bg-white/20 text-white border-white/30">
                <Wand2 className="w-3 h-3 mr-1" />
                Control avanzado
              </Badge>
            </div>
          </div>
        </div>

        {/* Contenido principal */}
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Panel de Control */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="lg:col-span-1"
            >
              <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Settings className="h-5 w-5 text-purple-600" />
                    Panel de Control
                  </CardTitle>
                  <CardDescription>
                    Configura y transfiere el estilo entre imágenes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Imagen Original */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Imagen Original</Label>
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                      onClick={() => initImageInputRef.current?.click()}
                    >
                      {initImagePreview ? (
                        <div className="space-y-3">
                          <img
                            src={initImagePreview}
                            alt="Imagen original"
                            className="w-full h-32 object-cover rounded-lg"
                          />
                          <p className="text-sm text-gray-600">{initImage?.name}</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                          <p className="text-sm text-gray-600">
                            Haz clic para subir imagen original
                          </p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={initImageInputRef}
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleInitImageChange(file);
                      }}
                      className="hidden"
                    />
                  </div>

                  {/* Imagen de Estilo */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Imagen de Estilo</Label>
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                      onClick={() => styleImageInputRef.current?.click()}
                    >
                      {styleImagePreview ? (
                        <div className="space-y-3">
                          <img
                            src={styleImagePreview}
                            alt="Imagen de estilo"
                            className="w-full h-32 object-cover rounded-lg"
                          />
                          <p className="text-sm text-gray-600">{styleImage?.name}</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Palette className="w-8 h-8 text-gray-400 mx-auto" />
                          <p className="text-sm text-gray-600">
                            Haz clic para subir imagen de estilo
                          </p>
                        </div>
                      )}
                    </div>
                    <input
                      ref={styleImageInputRef}
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleStyleImageChange(file);
                      }}
                      className="hidden"
                    />
                  </div>

                  {/* Prompt opcional */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Descripción (Opcional)</Label>
                    <Textarea
                      placeholder="Describe el resultado deseado..."
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="min-h-[80px] resize-none"
                    />
                  </div>

                  {/* Prompt negativo opcional */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Prompt Negativo (Opcional)</Label>
                    <Textarea
                      placeholder="Describe lo que NO quieres..."
                      value={negativePrompt}
                      onChange={(e) => setNegativePrompt(e.target.value)}
                      className="min-h-[60px] resize-none"
                    />
                  </div>

                  {/* Controles de configuración */}
                  <div className="space-y-4">
                    {/* Fuerza del estilo */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label className="text-sm font-medium">Fuerza del Estilo</Label>
                        <span className="text-sm text-gray-500">{styleStrength[0].toFixed(2)}</span>
                      </div>
                      <Slider
                        value={styleStrength}
                        onValueChange={setStyleStrength}
                        max={1}
                        min={0}
                        step={0.01}
                        className="w-full"
                      />
                    </div>

                    {/* Fidelidad de composición */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label className="text-sm font-medium">Fidelidad de Composición</Label>
                        <span className="text-sm text-gray-500">{compositionFidelity[0].toFixed(2)}</span>
                      </div>
                      <Slider
                        value={compositionFidelity}
                        onValueChange={setCompositionFidelity}
                        max={1}
                        min={0}
                        step={0.01}
                        className="w-full"
                      />
                    </div>

                    {/* Fuerza del cambio */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label className="text-sm font-medium">Fuerza del Cambio</Label>
                        <span className="text-sm text-gray-500">{changeStrength[0].toFixed(2)}</span>
                      </div>
                      <Slider
                        value={changeStrength}
                        onValueChange={setChangeStrength}
                        max={1}
                        min={0.1}
                        step={0.01}
                        className="w-full"
                      />
                    </div>

                    {/* Formato de salida */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Formato de Salida</Label>
                      <Select value={outputFormat} onValueChange={(value: "jpeg" | "png" | "webp") => setOutputFormat(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="png">PNG</SelectItem>
                          <SelectItem value="jpeg">JPEG</SelectItem>
                          <SelectItem value="webp">WebP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>


                  </div>

                  {/* Botones de acción */}
                  <div className="space-y-3">
                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !initImage || !styleImage}
                      className="w-full h-12 text-lg bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          Transfiriendo estilo...
                        </>
                      ) : (
                        <>
                          <ArrowRight className="w-5 h-5 mr-2" />
                          Transferir Estilo
                        </>
                      )}
                    </Button>

                    {(initImage || styleImage) && (
                      <Button
                        onClick={handleClearImages}
                        variant="outline"
                        className="w-full"
                        size="sm"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Limpiar Imágenes
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Área de Visualización */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="latest" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Última Generación
                  </TabsTrigger>
                  <TabsTrigger value="saved" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    Guardados ({savedStyleTransfers.length})
                  </TabsTrigger>
                </TabsList>

                {/* Tab: Última Generación */}
                <TabsContent value="latest" className="mt-0">
                  <AnimatePresence mode="wait">
                    {currentStyleTransfer ? (
                      <motion.div
                        key="result"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                          <CardHeader className="pb-4">
                            <CardTitle className="flex items-center gap-2">
                              <Sparkles className="w-5 h-5 text-purple-600" />
                              Resultado de Transferencia de Estilo
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-6">
                            {/* Imagen resultado */}
                            <div className="relative group">
                              <img
                                src={currentStyleTransfer.resultImageUrl}
                                alt="Resultado de transferencia de estilo"
                                className="w-full rounded-lg shadow-lg"
                              />
                            </div>

                            {/* Información de la transferencia */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                              <div>
                                <h4 className="font-medium text-sm text-gray-700 mb-2">Imagen Original</h4>
                                <img
                                  src={currentStyleTransfer.initImageUrl}
                                  alt="Original"
                                  className="w-full h-24 object-cover rounded-md"
                                />
                                <p className="text-xs text-gray-500 mt-1">{currentStyleTransfer.initImageFilename}</p>
                              </div>
                              <div>
                                <h4 className="font-medium text-sm text-gray-700 mb-2">Imagen de Estilo</h4>
                                <img
                                  src={currentStyleTransfer.styleImageUrl}
                                  alt="Estilo"
                                  className="w-full h-24 object-cover rounded-md"
                                />
                                <p className="text-xs text-gray-500 mt-1">{currentStyleTransfer.styleImageFilename}</p>
                              </div>
                            </div>

                            {/* Configuración utilizada */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-blue-50 rounded-lg">
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Fuerza Estilo</p>
                                <p className="font-medium">{currentStyleTransfer.styleStrength.toFixed(2)}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Composición</p>
                                <p className="font-medium">{currentStyleTransfer.compositionFidelity.toFixed(2)}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Cambio</p>
                                <p className="font-medium">{currentStyleTransfer.changeStrength.toFixed(2)}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Formato</p>
                                <p className="font-medium uppercase">{currentStyleTransfer.outputFormat}</p>
                              </div>
                            </div>

                            {/* Botones de acción */}
                            <div className="flex flex-wrap gap-3">
                              <Button
                                onClick={handleDownload}
                                className="flex-1 min-w-[120px]"
                                variant="outline"
                              >
                                <Download className="w-4 h-4 mr-2" />
                                Descargar
                              </Button>
                              <Button
                                onClick={handleCopyToClipboard}
                                className="flex-1 min-w-[120px]"
                                variant="outline"
                              >
                                <Copy className="w-4 h-4 mr-2" />
                                Copiar
                              </Button>
                              <Button
                                onClick={handleToggleFavorite}
                                className={`flex-1 min-w-[120px] ${
                                  currentStyleTransferSaved
                                    ? "bg-red-500 hover:bg-red-600 text-white"
                                    : "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                                }`}
                              >
                                <Heart className={`w-4 h-4 mr-2 ${currentStyleTransferSaved ? "fill-current" : ""}`} />
                                {currentStyleTransferSaved ? "Quitar" : "Guardar"}
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="placeholder"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="text-center py-16"
                      >
                        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                          <CardContent className="py-16">
                            <div className="space-y-4">
                              <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto">
                                <Palette className="w-12 h-12 text-purple-600" />
                              </div>
                              <h3 className="text-xl font-semibold text-gray-700">
                                Listo para transferir estilo
                              </h3>
                              <p className="text-gray-500 max-w-md mx-auto">
                                Sube una imagen original y una imagen de estilo para comenzar la transferencia.
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </TabsContent>

                {/* Tab: Guardados */}
                <TabsContent value="saved" className="mt-0">
                  {savedStyleTransfers.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {savedStyleTransfers.map((savedStyleTransfer) => (
                        <motion.div
                          key={savedStyleTransfer.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-shadow">
                            <CardContent className="p-4">
                              <div className="space-y-4">
                                {/* Imagen resultado */}
                                <div className="relative group">
                                  <img
                                    src={savedStyleTransfer.resultImageUrl}
                                    alt="Transferencia guardada"
                                    className="w-full h-48 object-cover rounded-lg"
                                  />
                                </div>

                                {/* Información */}
                                <div className="space-y-2">
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <p className="text-sm font-medium text-gray-700">
                                        {savedStyleTransfer.initImageFilename} → {savedStyleTransfer.styleImageFilename}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {new Date(savedStyleTransfer.timestamp).toLocaleDateString()}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Configuración */}
                                  <div className="grid grid-cols-3 gap-2 text-xs">
                                    <div className="text-center p-2 bg-gray-50 rounded">
                                      <p className="text-gray-600">Estilo</p>
                                      <p className="font-medium">{savedStyleTransfer.styleStrength.toFixed(2)}</p>
                                    </div>
                                    <div className="text-center p-2 bg-gray-50 rounded">
                                      <p className="text-gray-600">Composición</p>
                                      <p className="font-medium">{savedStyleTransfer.compositionFidelity.toFixed(2)}</p>
                                    </div>
                                    <div className="text-center p-2 bg-gray-50 rounded">
                                      <p className="text-gray-600">Cambio</p>
                                      <p className="font-medium">{savedStyleTransfer.changeStrength.toFixed(2)}</p>
                                    </div>
                                  </div>
                                </div>

                                {/* Botones de acción */}
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      // Cargar la transferencia en la vista principal
                                      setCurrentStyleTransfer({
                                        id: savedStyleTransfer.id,
                                        initImageUrl: savedStyleTransfer.initImageUrl,
                                        styleImageUrl: savedStyleTransfer.styleImageUrl,
                                        resultImageUrl: savedStyleTransfer.resultImageUrl,
                                        initImageFilename: savedStyleTransfer.initImageFilename,
                                        styleImageFilename: savedStyleTransfer.styleImageFilename,
                                        prompt: savedStyleTransfer.prompt,
                                        negativePrompt: savedStyleTransfer.negativePrompt,
                                        styleStrength: savedStyleTransfer.styleStrength,
                                        compositionFidelity: savedStyleTransfer.compositionFidelity,
                                        changeStrength: savedStyleTransfer.changeStrength,
                                        seed: savedStyleTransfer.seed,
                                        outputFormat: savedStyleTransfer.outputFormat,
                                        timestamp: savedStyleTransfer.timestamp,
                                      });

                                      // Cambiar a la pestaña "Última Generación"
                                      setMainTab("latest");

                                      toast({
                                        title: "🖼️ Transferencia cargada",
                                        description: "Transferencia cargada en la vista principal.",
                                      });
                                    }}
                                    className="flex-1"
                                  >
                                    <Eye className="w-3 h-3 mr-1" />
                                    Ver
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={async () => {
                                      try {
                                        const response = await fetch(savedStyleTransfer.resultImageUrl);
                                        const blob = await response.blob();
                                        const url = window.URL.createObjectURL(blob);
                                        const a = document.createElement("a");
                                        a.href = url;
                                        a.download = `style-transfer-${savedStyleTransfer.id}.${savedStyleTransfer.outputFormat}`;
                                        document.body.appendChild(a);
                                        a.click();
                                        window.URL.revokeObjectURL(url);
                                        document.body.removeChild(a);

                                        toast({
                                          title: "Descarga iniciada",
                                          description: "La imagen se está descargando",
                                        });
                                      } catch (error) {
                                        toast({
                                          title: "Error",
                                          description: "No se pudo descargar la imagen",
                                          variant: "destructive",
                                        });
                                      }
                                    }}
                                    className="flex-1"
                                  >
                                    <Download className="w-3 h-3 mr-1" />
                                    Descargar
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      const updatedStyleTransfers = savedStyleTransfers.filter(
                                        (saved) => saved.id !== savedStyleTransfer.id
                                      );
                                      setSavedStyleTransfers(updatedStyleTransfers);

                                      toast({
                                        title: "🗑️ Eliminado",
                                        description: "Transferencia eliminada de guardados.",
                                      });
                                    }}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-16"
                    >
                      <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                        <CardContent className="py-16">
                          <div className="space-y-4">
                            <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto">
                              <Heart className="w-12 h-12 text-purple-500" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-700">
                              No hay transferencias guardadas
                            </h3>
                            <p className="text-gray-500 max-w-md mx-auto">
                              Las transferencias de estilo que guardes aparecerán aquí para acceso rápido.
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )}
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
