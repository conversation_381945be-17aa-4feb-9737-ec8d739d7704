import React, { useState, useEffect } from "react";
import { observer } from "mobx-react-lite";

// Polotno core imports
import { createStore } from "polotno/model/store";
import { Workspace } from "polotno/canvas/workspace";
import { SidePanel } from "polotno/side-panel";
import { Toolbar } from "polotno/toolbar/toolbar";
import { ZoomButtons } from "polotno/toolbar/zoom-buttons";
import { PolotnoContainer, SidePanelWrap, WorkspaceWrap } from "polotno/polotno-app";

// Blueprint CSS (required for Polotno)
import "@blueprintjs/core/lib/css/blueprint.css";
import "@blueprintjs/popover2/lib/css/blueprint-popover2.css";

// Custom Emma AI styles for Polotno sidebar buttons
const emmaPolotnoStyles = `
  /* SUPER AGGRESSIVE Emma AI Blue styling - Override everything */

  /* Target ALL possible tab selectors */
  .bp4-tab,
  .bp4-tabs .bp4-tab,
  [role="tab"],
  .polotno-side-panel .bp4-tab,
  .polotno-side-panel [role="tab"] {
    background-color: #f8f9fa !important;
    background-image: none !important;
    border: 1px solid #e1e5e9 !important;
    color: #6c757d !important;
    transition: all 0.2s ease !important;
    margin: 2px !important;
    border-radius: 6px !important;
  }

  /* Hover states - ALL selectors */
  .bp4-tab:hover,
  .bp4-tabs .bp4-tab:hover,
  [role="tab"]:hover,
  .polotno-side-panel .bp4-tab:hover,
  .polotno-side-panel [role="tab"]:hover {
    background-color: #e3f2fd !important;
    background-image: none !important;
    border-color: #3018ef !important;
    color: #3018ef !important;
    transform: translateY(-1px) !important;
  }

  /* Active/Selected states - EMMA BLUE */
  .bp4-tab[aria-selected="true"],
  .bp4-tabs .bp4-tab[aria-selected="true"],
  [role="tab"][aria-selected="true"],
  .polotno-side-panel .bp4-tab[aria-selected="true"],
  .polotno-side-panel [role="tab"][aria-selected="true"],
  .bp4-tab.bp4-tab-selected,
  .bp4-tabs .bp4-tab.bp4-tab-selected {
    background: linear-gradient(135deg, #3018ef 0%, #4f46e5 100%) !important;
    background-image: linear-gradient(135deg, #3018ef 0%, #4f46e5 100%) !important;
    border-color: #3018ef !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(48, 24, 239, 0.3) !important;
  }

  /* Active hover states */
  .bp4-tab[aria-selected="true"]:hover,
  .bp4-tabs .bp4-tab[aria-selected="true"]:hover,
  [role="tab"][aria-selected="true"]:hover,
  .polotno-side-panel .bp4-tab[aria-selected="true"]:hover,
  .polotno-side-panel [role="tab"][aria-selected="true"]:hover {
    background: linear-gradient(135deg, #2614d4 0%, #4338ca 100%) !important;
    background-image: linear-gradient(135deg, #2614d4 0%, #4338ca 100%) !important;
    transform: translateY(-1px) !important;
  }

  /* Icons styling - make sure they're visible */
  .bp4-tab svg,
  .bp4-tabs .bp4-tab svg,
  [role="tab"] svg {
    filter: none !important;
    fill: currentColor !important;
  }

  .bp4-tab[aria-selected="true"] svg,
  .bp4-tabs .bp4-tab[aria-selected="true"] svg,
  [role="tab"][aria-selected="true"] svg {
    filter: brightness(0) invert(1) !important;
    fill: white !important;
  }

  /* Tab list container */
  .bp4-tab-list,
  .bp4-tabs .bp4-tab-list {
    background-color: #ffffff !important;
    border-bottom: 2px solid #f1f3f4 !important;
    padding: 8px !important;
  }

  /* Panel content */
  .bp4-tab-panel {
    border-left: 3px solid #3018ef !important;
    background-color: #fafbfc !important;
  }

  /* Side panel container */
  .polotno-side-panel,
  [data-testid="side-panel"] {
    border-right: 1px solid #e1e5e9 !important;
    background-color: #ffffff !important;
  }

  /* Workspace styling */
  .polotno-workspace {
    background-color: #f8f9fa !important;
  }

  /* Force override any inline styles */
  .bp4-tab[style] {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
  }

  .bp4-tab[aria-selected="true"][style] {
    background: linear-gradient(135deg, #3018ef 0%, #4f46e5 100%) !important;
    color: white !important;
  }
`;

// Props interface
interface PolotnoStudioProps {
  width?: number;
  height?: number;
  initialText?: string;
  platform?: string;
  initialImageUrl?: string;
  onPreview?: (dataUrl: string) => void;
  onSave?: (json: any) => void;
  className?: string;
  style?: React.CSSProperties;
}

// Main Polotno Studio Component
const PolotnoStudio: React.FC<PolotnoStudioProps> = ({
  width = 1080,
  height = 1080,
  initialText = "",
  platform = "instagram",
  initialImageUrl,
  onPreview,
  onSave,
  className = "",
  style = {},
}) => {
  // Create Polotno store with simple configuration
  const [store] = useState(() => {
    // Use environment API key or demo key
    const apiKey = import.meta.env.VITE_POLOTNO_API_KEY || "nFA5H9elEytDyPyvKL7T";

    return createStore({
      key: apiKey,
      showCredit: true, // Show Polotno credit (required for free usage)
    });
  });

  // Initialize page when store is ready
  useEffect(() => {
    console.log("🔄 PolotnoStudio useEffect ejecutándose");
    console.log("📊 Store state:", {
      storeExists: !!store,
      pagesLength: store?.pages?.length || 0,
      width,
      height,
      initialImageUrl: initialImageUrl ? "SÍ" : "NO"
    });

    if (store && store.pages.length === 0) {
      console.log("🆕 Creando nueva página en Polotno");

      // Create initial page
      const page = store.addPage({
        width,
        height,
      });

      console.log("✅ Página creada, ID:", page.id);

      // Add initial text if provided
      if (initialText) {
        console.log("📝 Agregando texto inicial:", initialText);
        page.addElement({
          type: "text",
          x: width / 2,
          y: height / 2,
          width: width * 0.8,
          height: 100,
          text: initialText,
          fontSize: Math.max(24, width / 30),
          fontFamily: "Arial",
          fill: "#000000",
          align: "center",
        });
      }

      // Add initial image if provided
      if (initialImageUrl) {
        console.log("🎨 PolotnoStudio: INICIANDO agregado de imagen");
        console.log("🔗 PolotnoStudio: URL completa:", initialImageUrl);
        console.log("🔗 PolotnoStudio: URL tipo:", initialImageUrl.startsWith('data:') ? 'BASE64' : initialImageUrl.startsWith('blob:') ? 'BLOB' : 'HTTP');
        console.log("📏 PolotnoStudio: Canvas size:", width, "x", height);

        try {
          const imageElement = page.addElement({
            type: "image",
            x: 0,
            y: 0,
            width: width,
            height: height,
            src: initialImageUrl,
          });
          console.log("✅ PolotnoStudio: Imagen agregada al canvas exitosamente");
          console.log("🆔 PolotnoStudio: Element ID:", imageElement?.id);
          console.log("📊 PolotnoStudio: Total elementos en página:", page.children.length);
        } catch (error) {
          console.error("❌ PolotnoStudio: Error agregando imagen:", error);
        }
      } else {
        console.log("❌ PolotnoStudio: No se recibió initialImageUrl");
        console.log("📋 PolotnoStudio props recibidas:", { width, height, initialText, initialImageUrl });
      }
    } else if (store && store.pages.length > 0) {
      console.log("⚠️ PolotnoStudio: Ya existe una página, no se agrega imagen");
      console.log("📊 Páginas existentes:", store.pages.length);
    } else {
      console.log("⚠️ PolotnoStudio: Store no está listo");
    }
  }, [store, width, height, initialText, initialImageUrl]);

  // Separate effect to handle image loading when initialImageUrl changes
  useEffect(() => {
    if (store && store.pages.length > 0 && initialImageUrl) {
      console.log("🔄 PolotnoStudio: initialImageUrl cambió, intentando agregar imagen a página existente");
      const page = store.pages[0];

      // Check if image already exists
      const hasImage = page.children.some((child: any) => child.type === 'image');
      if (!hasImage) {
        console.log("🎨 Agregando imagen a página existente");
        try {
          const imageElement = page.addElement({
            type: "image",
            x: 0,
            y: 0,
            width: width,
            height: height,
            src: initialImageUrl,
          });
          console.log("✅ Imagen agregada a página existente, ID:", imageElement?.id);
        } catch (error) {
          console.error("❌ Error agregando imagen a página existente:", error);
        }
      } else {
        console.log("ℹ️ Ya existe una imagen en la página");
      }
    }
  }, [initialImageUrl, store]);

  // Inject Emma AI styles for Polotno sidebar - AGGRESSIVE APPROACH
  useEffect(() => {
    const applyStyles = () => {
      // Remove existing styles
      const existingStyles = document.getElementById('emma-polotno-styles');
      if (existingStyles) {
        existingStyles.remove();
      }

      // Create and inject new styles
      const styleElement = document.createElement('style');
      styleElement.textContent = emmaPolotnoStyles;
      styleElement.id = 'emma-polotno-styles';
      document.head.appendChild(styleElement);

      console.log('Emma AI styles applied to Polotno sidebar');
    };

    // Apply immediately
    applyStyles();

    // Also apply after a short delay to ensure Polotno is loaded
    const timer1 = setTimeout(applyStyles, 100);
    const timer2 = setTimeout(applyStyles, 500);
    const timer3 = setTimeout(applyStyles, 1000);

    // Cleanup on unmount
    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      const styles = document.getElementById('emma-polotno-styles');
      if (styles) {
        styles.remove();
      }
    };
  }, []);

  // Handle high-quality preview generation
  const handlePreview = async () => {
    if (onPreview && store.pages[0]) {
      try {
        const dataUrl = await store.pages[0].toDataURL({
          pixelRatio: 3, // Ultra high quality
          mimeType: "image/png",
        });
        onPreview(dataUrl);
      } catch (error) {
        console.error("Error generating preview:", error);
      }
    }
  };

  // Handle export as PNG
  const handleExportPNG = async () => {
    if (store.pages[0]) {
      try {
        const dataUrl = await store.pages[0].toDataURL({
          pixelRatio: 3,
          mimeType: "image/png",
        });
        const link = document.createElement("a");
        link.download = `emma-design-${Date.now()}.png`;
        link.href = dataUrl;
        link.click();
      } catch (error) {
        console.error("Error exporting PNG:", error);
      }
    }
  };

  // Handle export as JPG
  const handleExportJPG = async () => {
    if (store.pages[0]) {
      try {
        const dataUrl = await store.pages[0].toDataURL({
          pixelRatio: 3,
          mimeType: "image/jpeg",
          quality: 0.95,
        });
        const link = document.createElement("a");
        link.download = `emma-design-${Date.now()}.jpg`;
        link.href = dataUrl;
        link.click();
      } catch (error) {
        console.error("Error exporting JPG:", error);
      }
    }
  };

  // Handle export as PDF
  const handleExportPDF = async () => {
    if (store.pages[0]) {
      try {
        const pdf = await store.toPDF();
        const url = URL.createObjectURL(pdf);
        const link = document.createElement("a");
        link.download = `emma-design-${Date.now()}.pdf`;
        link.href = url;
        link.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Error exporting PDF:", error);
      }
    }
  };

  // Handle save project
  const handleSave = () => {
    if (onSave) {
      const json = store.toJSON();
      onSave(json);
    } else {
      // Default save to localStorage
      const json = store.toJSON();
      localStorage.setItem(`emma-design-${Date.now()}`, JSON.stringify(json));
      alert("Design saved to browser storage!");
    }
  };

  // Handle load project
  const handleLoad = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const json = JSON.parse(e.target?.result as string);
            store.loadJSON(json);
          } catch (error) {
            console.error("Error loading design:", error);
            alert("Error loading design file");
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // Professional toolbar with all features
  const CustomToolbar = () => (
    <div style={{
      display: "flex",
      alignItems: "center",
      gap: "10px",
      padding: "8px 15px",
      borderBottom: "1px solid #e1e5e9",
      backgroundColor: "#f8f9fa"
    }}>
      {/* Standard Polotno Toolbar */}
      <Toolbar store={store} />

      {/* Emma Branding */}
      <div style={{
        marginLeft: "auto",
        display: "flex",
        alignItems: "center",
        gap: "12px"
      }}>
        <span style={{
          fontSize: "12px",
          color: "#666",
          fontWeight: "500"
        }}>
          Emma Studio
        </span>

        {/* File Operations */}
        <div style={{ display: "flex", gap: "6px" }}>
          <button
            onClick={handleLoad}
            style={{
              padding: "6px 10px",
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "12px",
              fontWeight: "500",
            }}
            title="Load Design"
          >
            📁 Load
          </button>

          <button
            onClick={handleSave}
            style={{
              padding: "6px 10px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "12px",
              fontWeight: "500",
            }}
            title="Save Design"
          >
            💾 Save
          </button>
        </div>

        {/* Export Options */}
        <div style={{ display: "flex", gap: "6px" }}>
          {onPreview && (
            <button
              onClick={handlePreview}
              style={{
                padding: "6px 10px",
                backgroundColor: "#3018ef",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "12px",
                fontWeight: "500",
              }}
              title="Preview"
            >
              👁️ Preview
            </button>
          )}

          <button
            onClick={handleExportPNG}
            style={{
              padding: "6px 10px",
              backgroundColor: "#17a2b8",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "12px",
              fontWeight: "500",
            }}
            title="Export as PNG"
          >
            🖼️ PNG
          </button>

          <button
            onClick={handleExportJPG}
            style={{
              padding: "6px 10px",
              backgroundColor: "#fd7e14",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "12px",
              fontWeight: "500",
            }}
            title="Export as JPG"
          >
            📷 JPG
          </button>

          <button
            onClick={handleExportPDF}
            style={{
              padding: "6px 10px",
              backgroundColor: "#dd3a5a",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "12px",
              fontWeight: "500",
            }}
            title="Export as PDF"
          >
            📄 PDF
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div
      className={`polotno-studio ${className}`}
      style={{
        width: "100%",
        height: "100%",
        minHeight: "600px",
        ...style
      }}
    >
      <PolotnoContainer style={{ width: "100%", height: "100%" }}>
        <SidePanelWrap>
          <SidePanel store={store} />
        </SidePanelWrap>
        <WorkspaceWrap>
          <CustomToolbar />
          <div style={{ flex: 1, position: "relative" }}>
            <Workspace store={store} />
            <ZoomButtons store={store} />
          </div>
        </WorkspaceWrap>
      </PolotnoContainer>
    </div>
  );
};

export default observer(PolotnoStudio);
export { PolotnoStudio };
