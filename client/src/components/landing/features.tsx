import { useRef } from "react";
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion";

export default function Features() {
  const features = [
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
          />
        </svg>
      ),
      title: "Agentes IA",
      description:
        "Accede a expertos virtuales para cada tarea, desde SEO hasta Copywriting, disponibles 24/7 y adaptados a tus necesidades exactas.",
      points: [
        "Especialistas en múltiples disciplinas",
        "Personalidad y estilos adaptables",
        "Memoria contextual persistente",
      ],
      color: "blue",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
          />
        </svg>
      ),
      title: "Campañas Inteligentes",
      description:
        "Define objetivos y deja que tus agentes IA colaboren para crear, ejecutar y optimizar campañas completas automatizadas.",
      points: [
        "Generación de contenido multicanal",
        "Optimización automática en tiempo real",
        "Calendario editorial integrado",
      ],
      color: "pink",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          />
        </svg>
      ),
      title: "Herramientas de Marketing",
      description:
        "Suite completa de utilidades IA para análisis de competencia, investigación de palabras clave, generación de ideas y más.",
      points: [
        "Análisis SEO avanzado",
        "Investigación de audiencia",
        "Generador de ideas creativas",
      ],
      color: "green",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
      ),
      title: "Ads Central",
      description:
        "Centro de creación de anuncios con plantillas optimizadas para todas las plataformas publicitarias principales.",
      points: [
        "Anuncios de Facebook e Instagram",
        "Campañas de Google Ads",
        "Creativos para LinkedIn y YouTube",
      ],
      color: "purple",
    },
  ];

  // Create a tilt card component
  const TiltCard = ({
    children,
    color,
  }: {
    children: React.ReactNode;
    color: string;
  }) => {
    const cardRef = useRef<HTMLDivElement>(null);
    const mouseX = useMotionValue(0);
    const mouseY = useMotionValue(0);
    const springConfig = { damping: 15, stiffness: 150 };
    const rotateX = useSpring(
      useTransform(mouseY, [-0.5, 0.5], [7, -7]),
      springConfig,
    );
    const rotateY = useSpring(
      useTransform(mouseX, [-0.5, 0.5], [-7, 7]),
      springConfig,
    );

    return (
      <motion.div
        ref={cardRef}
        className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-8 h-full"
        style={{
          rotateX,
          rotateY,
          perspective: 1000,
        }}
        onMouseMove={(e) => {
          if (!cardRef.current) return;
          const rect = cardRef.current.getBoundingClientRect();
          const centerX = rect.x + rect.width / 2;
          const centerY = rect.y + rect.height / 2;
          mouseX.set((e.clientX - centerX) / rect.width);
          mouseY.set((e.clientY - centerY) / rect.height);
        }}
        onMouseLeave={() => {
          mouseX.set(0);
          mouseY.set(0);
        }}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-50px" }}
        transition={{ duration: 0.5 }}
        whileHover={{ boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
      >
        <motion.div
          style={{
            transformStyle: "preserve-3d",
            transform: "translateZ(50px)",
          }}
        >
          {children}
        </motion.div>
      </motion.div>
    );
  };

  return (
    <section id="caracteristicas" className="py-20 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Características Principales
          </h2>
          <p className="text-xl font-bold max-w-3xl mx-auto">
            Una plataforma completa con todas las herramientas que necesitas
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <TiltCard key={index} color={feature.color}>
              <div
                className={`w-20 h-20 bg-${feature.color}-500 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] flex items-center justify-center mb-6`}
              >
                {feature.icon}
              </div>
              <h3 className="text-2xl font-black mb-4">{feature.title}</h3>
              <p className="text-lg mb-6">{feature.description}</p>
              <ul className="space-y-3">
                {feature.points.map((point, i) => (
                  <motion.li
                    key={i}
                    className="flex items-center"
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.3 + i * 0.1 }}
                    whileHover={{ x: 5 }}
                  >
                    <motion.span
                      className={`w-6 h-6 bg-${feature.color}-500 rounded-md border-2 border-black flex items-center justify-center text-white font-bold mr-3`}
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      ✓
                    </motion.span>
                    <span className="font-medium">{point}</span>
                  </motion.li>
                ))}
              </ul>
            </TiltCard>
          ))}
        </div>
      </div>
    </section>
  );
}
