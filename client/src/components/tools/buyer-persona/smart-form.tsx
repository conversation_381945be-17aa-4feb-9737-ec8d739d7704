"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  Target,
  DollarSign,
  Users,
  Lightbulb,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  CheckCircle,
  MapPin,
  UserCheck,
  Building,
  Clock,
  Crown
} from "lucide-react";

// Import enhanced utilities (keeping original interface for compatibility)
import {
  getUserCountry as getCountry,
  isStepComplete as validateStepCompletion,
  generateFormDescription as buildDescription
} from './smart-form/utils';

interface SmartFormData {
  product_type: string;
  product_name: string;
  industry: string;
  target_audience: string;
  main_problem: string;
  price_range: string;
  unique_value: string;
  // Nuevos campos
  geographic_scope: string;
  target_country: string; // NUEVO: País específico
  target_regions: string[];
  sales_channels: string[];
  audience_knowledge: string;
  existing_audience: string;
  business_sizes: string[]; // CAMBIADO: Múl<PERSON>les tamaños
  problem_urgency: string;
  decision_maker: string;
}

interface SmartFormProps {
  onSubmit: (data: SmartFormData) => void;
  isLoading: boolean;
}

const INDUSTRIES = [
  { id: "tech", name: "Tecnología", icon: "💻", color: "bg-blue-50 border-blue-200 text-blue-700" },
  { id: "ecommerce", name: "E-commerce", icon: "🛒", color: "bg-green-50 border-green-200 text-green-700" },
  { id: "education", name: "Educación", icon: "🎓", color: "bg-purple-50 border-purple-200 text-purple-700" },
  { id: "health", name: "Salud", icon: "🏥", color: "bg-red-50 border-red-200 text-red-700" },
  { id: "finance", name: "Finanzas", icon: "💰", color: "bg-yellow-50 border-yellow-200 text-yellow-700" },
  { id: "food", name: "Alimentación", icon: "🍕", color: "bg-orange-50 border-orange-200 text-orange-700" },
  { id: "real-estate", name: "Inmobiliaria", icon: "🏠", color: "bg-indigo-50 border-indigo-200 text-indigo-700" },
  { id: "services", name: "Servicios", icon: "💼", color: "bg-gray-50 border-gray-200 text-gray-700" },
];

const PRODUCT_TYPES = [
  { id: "physical", name: "Producto Físico", icon: "📦", description: "Artículos tangibles" },
  { id: "software", name: "Software/SaaS", icon: "💻", description: "Aplicaciones y plataformas" },
  { id: "service", name: "Servicio", icon: "🤝", description: "Consultoría y servicios" },
  { id: "course", name: "Curso/Educación", icon: "🎓", description: "Formación y educación" },
  { id: "content", name: "Contenido Digital", icon: "📱", description: "Ebooks, videos, etc." },
];

const PRICE_RANGES = [
  { id: "free", name: "Gratis", range: "$0", color: "bg-gray-50" },
  { id: "low", name: "Económico", range: "$1 - $50", color: "bg-green-50" },
  { id: "medium", name: "Medio", range: "$51 - $500", color: "bg-blue-50" },
  { id: "high", name: "Premium", range: "$501 - $2,000", color: "bg-purple-50" },
  { id: "enterprise", name: "Enterprise", range: "$2,000+", color: "bg-orange-50" },
];

// Use enhanced country detection from utils
const getUserCountry = getCountry;

// Import comprehensive country list from constants
import { COUNTRIES } from './smart-form/constants';
import { CountrySelector } from './smart-form/country-selector';

const GEOGRAPHIC_SCOPES = [
  { id: "local", name: "Local", icon: "🏠", description: "Ciudad o región específica" },
  { id: "national", name: "Nacional", icon: "🏳️", description: "Todo el país" }, // Bandera genérica
  { id: "international", name: "Internacional", icon: "🌍", description: "Múltiples países" },
  { id: "online", name: "Online Global", icon: "💻", description: "Sin límites geográficos" },
];

const SALES_CHANNELS = [
  { id: "physical", name: "Tienda Física", icon: "🏪" },
  { id: "ecommerce", name: "E-commerce", icon: "🛒" },
  { id: "social", name: "Redes Sociales", icon: "📱" },
  { id: "marketplace", name: "Marketplace", icon: "🏬" },
  { id: "b2b", name: "Venta Directa B2B", icon: "🤝" },
  { id: "phone", name: "Teléfono/Call Center", icon: "📞" },
];

const AUDIENCE_KNOWLEDGE = [
  { id: "clear", name: "Tengo claro mi público", icon: "🎯", color: "bg-green-50 border-green-200 text-green-700" },
  { id: "general", name: "Tengo una idea general", icon: "💭", color: "bg-blue-50 border-blue-200 text-blue-700" },
  { id: "unsure", name: "No estoy seguro", icon: "🤔", color: "bg-yellow-50 border-yellow-200 text-yellow-700" },
  { id: "explore", name: "Quiero explorar nuevos segmentos", icon: "🔍", color: "bg-purple-50 border-purple-200 text-purple-700" },
];

const BUSINESS_SIZES = [
  { id: "b2c-students", name: "Estudiantes", icon: "🎓", type: "B2C" },
  { id: "b2c-young", name: "Profesionales Jóvenes", icon: "👨‍💼", type: "B2C" },
  { id: "b2c-families", name: "Familias", icon: "👨‍👩‍👧‍👦", type: "B2C" },
  { id: "b2c-seniors", name: "Jubilados", icon: "👴", type: "B2C" },
  { id: "freelancers", name: "Freelancers", icon: "💻", type: "B2B" },
  { id: "startups", name: "Startups (1-10)", icon: "🚀", type: "B2B" },
  { id: "sme", name: "PYMES (11-50)", icon: "🏢", type: "B2B" },
  { id: "medium", name: "Medianas (51-200)", icon: "🏬", type: "B2B" },
  { id: "enterprise", name: "Corporaciones (200+)", icon: "🏛️", type: "B2B" },
];

const URGENCY_LEVELS = [
  { id: "critical", name: "Crítico - Lo necesitan YA", icon: "🔥", color: "bg-red-50 border-red-200 text-red-700" },
  { id: "important", name: "Importante - Pueden esperar", icon: "⚡", color: "bg-orange-50 border-orange-200 text-orange-700" },
  { id: "nice", name: "Nice-to-have", icon: "✨", color: "bg-blue-50 border-blue-200 text-blue-700" },
  { id: "future", name: "Prevención/Mejora futura", icon: "🔮", color: "bg-purple-50 border-purple-200 text-purple-700" },
];

const DECISION_MAKERS = [
  { id: "individual", name: "Decisión Individual", icon: "👤", type: "B2C" },
  { id: "couple", name: "Decisión en Pareja", icon: "👫", type: "B2C" },
  { id: "family", name: "Decisión Familiar", icon: "👨‍👩‍👧‍👦", type: "B2C" },
  { id: "ceo", name: "CEO/Fundador", icon: "👑", type: "B2B" },
  { id: "manager", name: "Manager/Director", icon: "👨‍💼", type: "B2B" },
  { id: "team", name: "Equipo de Compras", icon: "👥", type: "B2B" },
  { id: "committee", name: "Comité de Decisión", icon: "🏛️", type: "B2B" },
];

export function SmartForm({ onSubmit, isLoading }: SmartFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [userCountry] = useState(() => getUserCountry()); // Detectar país automáticamente
  const [startTime] = useState(() => Date.now()); // Track form start time
  const [formData, setFormData] = useState<SmartFormData>({
    product_type: "",
    product_name: "",
    industry: "",
    target_audience: "",
    main_problem: "",
    price_range: "",
    unique_value: "",
    // Nuevos campos
    geographic_scope: "",
    target_country: userCountry.code, // Auto-detectado
    target_regions: [],
    sales_channels: [],
    audience_knowledge: "",
    existing_audience: "",
    business_sizes: [], // Ahora es array
    problem_urgency: "",
    decision_maker: "",
  });

  const steps = [
    {
      id: "product",
      title: "¿Qué vendes?",
      subtitle: "Cuéntanos sobre tu producto o servicio",
      icon: Package,
      color: "text-blue-600",
    },
    {
      id: "industry",
      title: "¿En qué industria?",
      subtitle: "Selecciona tu sector principal",
      icon: Target,
      color: "text-purple-600",
    },
    {
      id: "audience",
      title: "¿A quién se lo vendes?",
      subtitle: "Define tu audiencia objetivo",
      icon: Users,
      color: "text-green-600",
    },
    {
      id: "problem",
      title: "¿Qué problema resuelves?",
      subtitle: "El dolor principal de tus clientes",
      icon: Lightbulb,
      color: "text-orange-600",
    },
    {
      id: "pricing",
      title: "¿Cuál es tu rango de precio?",
      subtitle: "Ayúdanos a entender tu mercado",
      icon: DollarSign,
      color: "text-emerald-600",
    },
    {
      id: "geography",
      title: "¿Dónde vendes?",
      subtitle: "Define tu alcance geográfico",
      icon: MapPin,
      color: "text-blue-600",
    },
    {
      id: "knowledge",
      title: "¿Ya conoces tu público?",
      subtitle: "Nivel de conocimiento de tu audiencia",
      icon: UserCheck,
      color: "text-indigo-600",
    },
    {
      id: "business",
      title: "¿Qué tamaño de cliente?",
      subtitle: "B2C o B2B y tamaño de empresa",
      icon: Building,
      color: "text-cyan-600",
    },
    {
      id: "urgency",
      title: "¿Cuál es la urgencia?",
      subtitle: "Qué tan crítico es el problema",
      icon: Clock,
      color: "text-red-600",
    },
    {
      id: "decision",
      title: "¿Quién decide la compra?",
      subtitle: "Identifica al decisor final",
      icon: Crown,
      color: "text-yellow-600",
    },
  ];

  const updateFormData = (field: keyof SmartFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const updateArrayField = (field: keyof SmartFormData, value: string) => {
    setFormData(prev => {
      const currentArray = prev[field] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [field]: newArray };
    });
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    try {
      // Use enhanced description generation from utils
      const description = buildDescription(formData);

      // Submit with enhanced data structure
      onSubmit({
        ...formData,
        product_description: description,
        // Add metadata for better tracking
        timestamp: new Date().toISOString(),
        user_agent: navigator.userAgent,
        completion_time_seconds: Math.round((Date.now() - startTime) / 1000)
      } as any);
    } catch (error) {
      console.error('Error submitting form:', error);
      // Fallback to basic submission
      onSubmit(formData);
    }
  };

  // Use enhanced validation from utils
  const isStepComplete = (stepIndex: number) => {
    return validateStepCompletion(stepIndex, formData);
  };

  const canProceed = isStepComplete(currentStep);
  const isLastStep = currentStep === steps.length - 1;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">Formulario Inteligente</h2>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Paso {currentStep + 1} de {steps.length}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all
                ${index <= currentStep
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-500'
                }
                ${isStepComplete(index) && index < currentStep ? 'bg-green-600' : ''}
              `}>
                {isStepComplete(index) && index < currentStep ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  index + 1
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`
                  w-12 h-1 mx-2 rounded transition-all
                  ${index < currentStep ? 'bg-blue-600' : 'bg-gray-200'}
                `} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50">
        <CardContent className="p-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* Step Header */}
              <div className="text-center mb-8">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-blue-50 to-purple-50 mb-4`}>
                  {React.createElement(steps[currentStep].icon, {
                    className: `h-8 w-8 ${steps[currentStep].color}`
                  })}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {steps[currentStep].title}
                </h3>
                <p className="text-gray-600">
                  {steps[currentStep].subtitle}
                </p>
              </div>

              {/* Step Content */}
              <div className="space-y-6">
                {renderStepContent()}
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-100">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Anterior
            </Button>

            <div className="flex items-center gap-3">
              {isLastStep ? (
                <Button
                  onClick={handleSubmit}
                  disabled={!canProceed || isLoading}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-2 flex items-center gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  {isLoading ? "Generando..." : "Generar Buyer Personas"}
                </Button>
              ) : (
                <Button
                  onClick={nextStep}
                  disabled={!canProceed}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 flex items-center gap-2"
                >
                  Siguiente
                  <ArrowRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  function renderStepContent() {
    switch (currentStep) {
      case 0: // Product Type & Name
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium text-gray-700 mb-4 block">
                Tipo de producto o servicio
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {PRODUCT_TYPES.map((type) => (
                  <motion.div
                    key={type.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <button
                      onClick={() => updateFormData("product_type", type.id)}
                      className={`
                        w-full p-4 rounded-lg border-2 text-left transition-all
                        ${formData.product_type === type.id
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{type.icon}</span>
                        <div>
                          <div className="font-medium text-gray-900">{type.name}</div>
                          <div className="text-sm text-gray-500">{type.description}</div>
                        </div>
                      </div>
                    </button>
                  </motion.div>
                ))}
              </div>
            </div>

            {formData.product_type && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-3"
              >
                <Label htmlFor="product_name" className="text-base font-medium text-gray-700">
                  Nombre de tu {PRODUCT_TYPES.find(p => p.id === formData.product_type)?.name.toLowerCase()}
                </Label>
                <Input
                  id="product_name"
                  placeholder="Ej: Plataforma de gestión de proyectos"
                  value={formData.product_name}
                  onChange={(e) => updateFormData("product_name", e.target.value)}
                  className="text-lg py-3 border-2 focus:border-blue-500"
                />
              </motion.div>
            )}
          </div>
        );

      case 1: // Industry
        return (
          <div>
            <Label className="text-base font-medium text-gray-700 mb-4 block">
              Selecciona tu industria principal
            </Label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {INDUSTRIES.map((industry) => (
                <motion.button
                  key={industry.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => updateFormData("industry", industry.id)}
                  className={`
                    p-4 rounded-lg border-2 text-center transition-all
                    ${formData.industry === industry.id
                      ? `border-blue-500 ${industry.color} shadow-md`
                      : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                    }
                  `}
                >
                  <div className="text-3xl mb-2">{industry.icon}</div>
                  <div className="font-medium text-sm">{industry.name}</div>
                </motion.button>
              ))}
            </div>
          </div>
        );

      case 2: // Target Audience
        return (
          <div className="space-y-4">
            <Label htmlFor="target_audience" className="text-base font-medium text-gray-700">
              Describe tu audiencia objetivo
            </Label>
            <textarea
              id="target_audience"
              placeholder="Ej: Emprendedores de 25-40 años que buscan automatizar sus procesos de negocio..."
              value={formData.target_audience}
              onChange={(e) => updateFormData("target_audience", e.target.value)}
              rows={4}
              className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none text-lg resize-none"
            />
            <div className="text-sm text-gray-500">
              💡 Incluye edad, profesión, tamaño de empresa, ubicación, etc.
            </div>
          </div>
        );

      case 3: // Main Problem
        return (
          <div className="space-y-4">
            <Label htmlFor="main_problem" className="text-base font-medium text-gray-700">
              ¿Qué problema principal resuelves?
            </Label>
            <textarea
              id="main_problem"
              placeholder="Ej: Los emprendedores pierden mucho tiempo en tareas repetitivas y no pueden enfocarse en hacer crecer su negocio..."
              value={formData.main_problem}
              onChange={(e) => updateFormData("main_problem", e.target.value)}
              rows={4}
              className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none text-lg resize-none"
            />
            <div className="text-sm text-gray-500">
              💡 Describe el dolor, frustración o necesidad que tu producto soluciona
            </div>
          </div>
        );

      case 4: // Pricing
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium text-gray-700 mb-4 block">
                Rango de precio de tu producto/servicio
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {PRICE_RANGES.map((price) => (
                  <motion.button
                    key={price.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => updateFormData("price_range", price.id)}
                    className={`
                      p-4 rounded-lg border-2 text-center transition-all
                      ${formData.price_range === price.id
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : `border-gray-200 hover:border-gray-300 ${price.color}`
                      }
                    `}
                  >
                    <div className="font-bold text-lg text-gray-900">{price.name}</div>
                    <div className="text-sm text-gray-600">{price.range}</div>
                  </motion.button>
                ))}
              </div>
            </div>

            {formData.price_range && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-3"
              >
                <Label htmlFor="unique_value" className="text-base font-medium text-gray-700">
                  ¿Qué te hace único? (Opcional)
                </Label>
                <Input
                  id="unique_value"
                  placeholder="Ej: Somos los únicos que ofrecemos integración con IA..."
                  value={formData.unique_value}
                  onChange={(e) => updateFormData("unique_value", e.target.value)}
                  className="text-lg py-3 border-2 focus:border-blue-500"
                />
              </motion.div>
            )}
          </div>
        );

      case 5: // Geography
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium text-gray-700 mb-4 block">
                ¿Cuál es tu alcance geográfico?
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {GEOGRAPHIC_SCOPES.map((scope) => (
                  <motion.button
                    key={scope.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => updateFormData("geographic_scope", scope.id)}
                    className={`
                      p-4 rounded-lg border-2 text-left transition-all
                      ${formData.geographic_scope === scope.id
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{scope.icon}</span>
                      <div>
                        <div className="font-medium text-gray-900">{scope.name}</div>
                        <div className="text-sm text-gray-500">{scope.description}</div>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Enhanced Country Selector */}
            {(formData.geographic_scope === "local" || formData.geographic_scope === "national") && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <CountrySelector
                  selectedCountry={formData.target_country}
                  onChange={(countryCode) => updateFormData("target_country", countryCode)}
                  userCountry={userCountry}
                  label="¿En qué país?"
                  placeholder="Buscar países..."
                />
              </motion.div>
            )}

            {formData.geographic_scope && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <Label className="text-base font-medium text-gray-700">
                  ¿Cómo vendes? (Selecciona todos los que apliquen)
                </Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {SALES_CHANNELS.map((channel) => (
                    <motion.button
                      key={channel.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateArrayField("sales_channels", channel.id)}
                      className={`
                        p-3 rounded-lg border-2 text-center transition-all
                        ${formData.sales_channels.includes(channel.id)
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="text-2xl mb-1">{channel.icon}</div>
                      <div className="text-sm font-medium">{channel.name}</div>
                    </motion.button>
                  ))}
                </div>
                <div className="text-sm text-gray-500">
                  💡 Selecciona todos los canales que usas o planeas usar
                </div>
              </motion.div>
            )}
          </div>
        );

      case 6: // Audience Knowledge
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium text-gray-700 mb-4 block">
                ¿Qué tan bien conoces a tu público objetivo?
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {AUDIENCE_KNOWLEDGE.map((knowledge) => (
                  <motion.button
                    key={knowledge.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => updateFormData("audience_knowledge", knowledge.id)}
                    className={`
                      p-4 rounded-lg border-2 text-left transition-all
                      ${formData.audience_knowledge === knowledge.id
                        ? `border-blue-500 ${knowledge.color} shadow-md`
                        : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{knowledge.icon}</span>
                      <div className="font-medium">{knowledge.name}</div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {(formData.audience_knowledge === "clear" || formData.audience_knowledge === "general") && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-3"
              >
                <Label htmlFor="existing_audience" className="text-base font-medium text-gray-700">
                  Describe brevemente tu público actual
                </Label>
                <textarea
                  id="existing_audience"
                  placeholder="Ej: Principalmente mujeres de 25-40 años, profesionales del marketing, con ingresos medios-altos..."
                  value={formData.existing_audience}
                  onChange={(e) => updateFormData("existing_audience", e.target.value)}
                  rows={3}
                  className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none text-lg resize-none"
                />
              </motion.div>
            )}
          </div>
        );

      case 7: // Business Size
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium text-gray-700 mb-4 block">
                ¿Qué tipo y tamaño de cliente tienes? (Selecciona todos los que apliquen)
              </Label>

              {/* B2C Section */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                  <span className="text-lg">👤</span>
                  Consumidores (B2C)
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {BUSINESS_SIZES.filter(size => size.type === "B2C").map((size) => (
                    <motion.button
                      key={size.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateArrayField("business_sizes", size.id)}
                      className={`
                        p-3 rounded-lg border-2 text-center transition-all
                        ${formData.business_sizes.includes(size.id)
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="text-2xl mb-1">{size.icon}</div>
                      <div className="text-sm font-medium">{size.name}</div>
                      {formData.business_sizes.includes(size.id) && (
                        <div className="text-xs text-blue-600 mt-1">✓ Seleccionado</div>
                      )}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* B2B Section */}
              <div>
                <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                  <span className="text-lg">🏢</span>
                  Empresas (B2B)
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {BUSINESS_SIZES.filter(size => size.type === "B2B").map((size) => (
                    <motion.button
                      key={size.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateArrayField("business_sizes", size.id)}
                      className={`
                        p-3 rounded-lg border-2 text-center transition-all
                        ${formData.business_sizes.includes(size.id)
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="text-2xl mb-1">{size.icon}</div>
                      <div className="text-sm font-medium">{size.name}</div>
                      {formData.business_sizes.includes(size.id) && (
                        <div className="text-xs text-blue-600 mt-1">✓ Seleccionado</div>
                      )}
                    </motion.button>
                  ))}
                </div>
              </div>

              <div className="text-sm text-gray-500 mt-4">
                💡 Puedes seleccionar múltiples tipos de cliente para generar personas más diversas
              </div>
            </div>
          </div>
        );

      case 8: // Problem Urgency
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium text-gray-700 mb-4 block">
                ¿Qué tan urgente es el problema que resuelves?
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {URGENCY_LEVELS.map((urgency) => (
                  <motion.button
                    key={urgency.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => updateFormData("problem_urgency", urgency.id)}
                    className={`
                      p-4 rounded-lg border-2 text-left transition-all
                      ${formData.problem_urgency === urgency.id
                        ? `border-blue-500 ${urgency.color} shadow-md`
                        : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{urgency.icon}</span>
                      <div className="font-medium">{urgency.name}</div>
                    </div>
                  </motion.button>
                ))}
              </div>
              <div className="text-sm text-gray-500 mt-4">
                💡 Esto nos ayuda a entender el ciclo de compra y la motivación de tus clientes
              </div>
            </div>
          </div>
        );

      case 9: // Decision Maker
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium text-gray-700 mb-4 block">
                ¿Quién toma la decisión de compra?
              </Label>

              {/* B2C Section */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                  <span className="text-lg">👤</span>
                  Decisiones de Consumo (B2C)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {DECISION_MAKERS.filter(maker => maker.type === "B2C").map((maker) => (
                    <motion.button
                      key={maker.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateFormData("decision_maker", maker.id)}
                      className={`
                        p-4 rounded-lg border-2 text-left transition-all
                        ${formData.decision_maker === maker.id
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{maker.icon}</span>
                        <div className="font-medium">{maker.name}</div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* B2B Section */}
              <div>
                <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                  <span className="text-lg">🏢</span>
                  Decisiones Empresariales (B2B)
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {DECISION_MAKERS.filter(maker => maker.type === "B2B").map((maker) => (
                    <motion.button
                      key={maker.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateFormData("decision_maker", maker.id)}
                      className={`
                        p-3 rounded-lg border-2 text-center transition-all
                        ${formData.decision_maker === maker.id
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="text-2xl mb-1">{maker.icon}</div>
                      <div className="text-sm font-medium">{maker.name}</div>
                    </motion.button>
                  ))}
                </div>
              </div>

              <div className="text-sm text-gray-500 mt-4">
                💡 Identificar al decisor correcto es clave para enfocar tu estrategia de ventas
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  }
}
