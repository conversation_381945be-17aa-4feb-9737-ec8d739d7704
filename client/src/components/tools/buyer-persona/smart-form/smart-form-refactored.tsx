"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  Target,
  Users,
  Lightbulb,
  DollarSign,
  MapPin,
  UserCheck,
  Building,
  Clock,
  Crown,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  CheckCircle
} from "lucide-react";

import { SmartFormData, SmartFormProps, FormStep } from './types';
import { getUserCountry, isStepComplete, generateFormDescription } from './utils';
import { ProductStep, IndustryStep, AudienceStep, ProblemStep, PricingStep } from './steps';

const FORM_STEPS: FormStep[] = [
  { id: "product", title: "¿Qué vendes?", subtitle: "Cuéntanos sobre tu producto o servicio", icon: Package, color: "text-blue-600" },
  { id: "industry", title: "¿En qué industria?", subtitle: "Selecciona tu sector principal", icon: Target, color: "text-purple-600" },
  { id: "audience", title: "¿A quién se lo vendes?", subtitle: "Define tu audiencia objetivo", icon: Users, color: "text-green-600" },
  { id: "problem", title: "¿Qué problema resuelves?", subtitle: "El dolor principal de tus clientes", icon: Lightbulb, color: "text-orange-600" },
  { id: "pricing", title: "¿Cuál es tu rango de precio?", subtitle: "Ayúdanos a entender tu mercado", icon: DollarSign, color: "text-emerald-600" },
  { id: "geography", title: "¿Dónde vendes?", subtitle: "Define tu alcance geográfico", icon: MapPin, color: "text-blue-600" },
  { id: "knowledge", title: "¿Ya conoces tu público?", subtitle: "Nivel de conocimiento de tu audiencia", icon: UserCheck, color: "text-indigo-600" },
  { id: "business", title: "¿Qué tamaño de cliente?", subtitle: "B2C o B2B y tamaño de empresa", icon: Building, color: "text-cyan-600" },
  { id: "urgency", title: "¿Cuál es la urgencia?", subtitle: "Qué tan crítico es el problema", icon: Clock, color: "text-red-600" },
  { id: "decision", title: "¿Quién decide la compra?", subtitle: "Identifica al decisor final", icon: Crown, color: "text-yellow-600" },
];

export function SmartFormRefactored({ onSubmit, isLoading }: SmartFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [userCountry] = useState(() => getUserCountry());
  const [formData, setFormData] = useState<SmartFormData>({
    product_type: "",
    product_name: "",
    industry: "",
    target_audience: "",
    main_problem: "",
    price_range: "",
    unique_value: "",
    geographic_scope: "",
    target_country: userCountry.code,
    target_regions: [],
    sales_channels: [],
    audience_knowledge: "",
    existing_audience: "",
    business_sizes: [],
    problem_urgency: "",
    decision_maker: "",
  });

  const updateFormData = (field: keyof SmartFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const updateArrayField = (field: keyof SmartFormData, value: string) => {
    setFormData(prev => {
      const currentArray = prev[field] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [field]: newArray };
    });
  };

  const nextStep = () => {
    if (currentStep < FORM_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    const description = generateFormDescription(formData);
    onSubmit({ ...formData, product_description: description } as any);
  };

  const canProceed = isStepComplete(currentStep, formData);
  const isLastStep = currentStep === FORM_STEPS.length - 1;

  const renderStepContent = () => {
    const stepProps = { formData, updateFormData, updateArrayField, userCountry };
    
    switch (currentStep) {
      case 0: return <ProductStep {...stepProps} />;
      case 1: return <IndustryStep {...stepProps} />;
      case 2: return <AudienceStep {...stepProps} />;
      case 3: return <ProblemStep {...stepProps} />;
      case 4: return <PricingStep {...stepProps} />;
      default: 
        return (
          <div className="text-center py-8">
            <div className="text-6xl mb-4">🚧</div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">Paso en construcción</h3>
            <p className="text-gray-500">Este paso se está refactorizando para mejor rendimiento</p>
          </div>
        );
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">Formulario Inteligente</h2>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Paso {currentStep + 1} de {FORM_STEPS.length}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          {FORM_STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all
                ${index <= currentStep
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-500'
                }
                ${isStepComplete(index, formData) && index < currentStep ? 'bg-green-600' : ''}
              `}>
                {isStepComplete(index, formData) && index < currentStep ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  index + 1
                )}
              </div>
              {index < FORM_STEPS.length - 1 && (
                <div className={`
                  w-12 h-1 mx-2 rounded transition-all
                  ${index < currentStep ? 'bg-blue-600' : 'bg-gray-200'}
                `} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50">
        <CardContent className="p-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* Step Header */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-blue-50 to-purple-50 mb-4">
                  {React.createElement(FORM_STEPS[currentStep].icon, {
                    className: `h-8 w-8 ${FORM_STEPS[currentStep].color}`
                  })}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {FORM_STEPS[currentStep].title}
                </h3>
                <p className="text-gray-600">
                  {FORM_STEPS[currentStep].subtitle}
                </p>
              </div>

              {/* Step Content */}
              <div className="space-y-6">
                {renderStepContent()}
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-100">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Anterior
            </Button>

            <div className="flex items-center gap-3">
              {isLastStep ? (
                <Button
                  onClick={handleSubmit}
                  disabled={!canProceed || isLoading}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-2 flex items-center gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  {isLoading ? "Generando..." : "Generar Buyer Personas"}
                </Button>
              ) : (
                <Button
                  onClick={nextStep}
                  disabled={!canProceed}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 flex items-center gap-2"
                >
                  Siguiente
                  <ArrowRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
