import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  MessageCircle, 
  X, 
  Send, 
  Sparkles, 
  Wand2, 
  Target, 
  Lightbulb,
  RefreshCw,
  Minimize2,
  Maximize2
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

// Importar imagen de Emma y servicios
import EmmaProfile from "@/assets/emma-profile.png";
import { contentApiService } from "@/services/content-api-service";

interface Message {
  id: string;
  sender: "user" | "emma";
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface EmmaAdAssistantProps {
  platform: string;
  currentPrompt?: string;
  onPromptSuggestion?: (prompt: string) => void;
  onHeadlineSuggestion?: (headline: string) => void;
  onPunchlineSuggestion?: (punchline: string) => void;
  onCtaSuggestion?: (cta: string) => void;
  onSizeRecommendation?: (size: string) => void;
  className?: string;
}

const EmmaAdAssistant: React.FC<EmmaAdAssistantProps> = ({
  platform,
  currentPrompt = "",
  onPromptSuggestion,
  onHeadlineSuggestion,
  onPunchlineSuggestion,
  onCtaSuggestion,
  onSizeRecommendation,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Sugerencias inteligentes basadas en la plataforma
  const platformSuggestions = {
    facebook: [
      "Crea un anuncio para promocionar un producto tecnológico",
      "Diseña un anuncio de servicio profesional",
      "Genera un anuncio para evento especial",
      "Crea un anuncio de marca personal"
    ],
    instagram: [
      "Diseña un post para lifestyle brand",
      "Crea contenido para marca de moda",
      "Genera un anuncio de producto visual",
      "Diseña contenido para influencer"
    ],
    linkedin: [
      "Crea un anuncio B2B profesional",
      "Diseña contenido para servicios corporativos",
      "Genera anuncio de software empresarial",
      "Crea contenido para consultoría"
    ],
    youtube: [
      "Diseña thumbnail para tutorial",
      "Crea banner para canal educativo",
      "Genera thumbnail para review",
      "Diseña contenido para vlog"
    ]
  };

  // Mensaje de bienvenida inicial
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: "welcome",
        sender: "emma",
        content: `¡Hola! 👋 Soy Emma, entrenada con millones de campañas exitosas de marketing. Puedo generar automáticamente headlines, punchlines, CTAs y optimizar tu descripción para maximizar conversiones. Solo dime qué necesitas y yo llenaré los campos directamente. ¿En qué te ayudo?`,
        timestamp: new Date(),
        suggestions: [
          "Generar headline atractivo",
          "Crear punchline persuasivo",
          "Añadir CTA efectivo",
          "Optimizar descripción"
        ]
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, platform]);

  // Auto scroll al final
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Función para detectar y llenar campos automáticamente
  const handleAutomaticFieldPopulation = async (userInput: string, emmaResponse: string) => {
    const input = userInput.toLowerCase();
    const response = emmaResponse.toLowerCase();

    // Detectar solicitudes específicas de campos
    if (input.includes('headline') || input.includes('título') || input.includes('encabezado')) {
      if (onHeadlineSuggestion) {
        // Extraer headline de la respuesta de Emma o generar uno nuevo
        const extractedHeadline = extractFieldFromResponse(emmaResponse, 'headline') ||
                                 await generateSpecificField('headline', userInput);
        if (extractedHeadline) {
          onHeadlineSuggestion(extractedHeadline);
        }
      }
    }

    if (input.includes('punchline') || input.includes('subtítulo') || input.includes('descripción corta')) {
      if (onPunchlineSuggestion) {
        const extractedPunchline = extractFieldFromResponse(emmaResponse, 'punchline') ||
                                  await generateSpecificField('punchline', userInput);
        if (extractedPunchline) {
          onPunchlineSuggestion(extractedPunchline);
        }
      }
    }

    if (input.includes('cta') || input.includes('botón') || input.includes('call to action') || input.includes('llamada a la acción')) {
      if (onCtaSuggestion) {
        const extractedCta = extractFieldFromResponse(emmaResponse, 'cta') ||
                            await generateSpecificField('cta', userInput);
        if (extractedCta) {
          onCtaSuggestion(extractedCta);
        }
      }
    }

    if (input.includes('prompt') || input.includes('descripción') || input.includes('producto')) {
      if (onPromptSuggestion) {
        const extractedPrompt = extractFieldFromResponse(emmaResponse, 'prompt') ||
                               await generateSpecificField('prompt', userInput);
        if (extractedPrompt) {
          onPromptSuggestion(extractedPrompt);
        }
      }
    }
  };

  // Función para extraer campos específicos de la respuesta de Emma
  const extractFieldFromResponse = (response: string, fieldType: string): string | null => {
    // Buscar patrones comunes en las respuestas
    const patterns = {
      headline: [/"([^"]{1,40})"/g, /headline[:\s]+"?([^".\n]{1,40})"?/gi, /título[:\s]+"?([^".\n]{1,40})"?/gi],
      punchline: [/"([^"]{1,40})"/g, /punchline[:\s]+"?([^".\n]{1,40})"?/gi, /subtítulo[:\s]+"?([^".\n]{1,40})"?/gi],
      cta: [/"([^"]{1,25})"/g, /cta[:\s]+"?([^".\n]{1,25})"?/gi, /botón[:\s]+"?([^".\n]{1,25})"?/gi],
      prompt: [/"([^"]{10,200})"/g, /prompt[:\s]+"?([^".\n]{10,200})"?/gi]
    };

    const fieldPatterns = patterns[fieldType as keyof typeof patterns] || [];

    for (const pattern of fieldPatterns) {
      const match = pattern.exec(response);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  };

  // Función para generar campos específicos usando el agente real de Emma
  const generateSpecificField = async (fieldType: string, context: string): Promise<string | null> => {
    try {
      const prompts = {
        headline: `Como Emma, genera SOLO un headline atractivo de máximo 40 caracteres para un anuncio de ${platform}. Contexto: ${context}. Responde únicamente con el headline, sin comillas ni explicaciones.`,
        punchline: `Como Emma, genera SOLO un punchline persuasivo de máximo 40 caracteres para un anuncio de ${platform}. Contexto: ${context}. Responde únicamente con el punchline, sin comillas ni explicaciones.`,
        cta: `Como Emma, genera SOLO un CTA efectivo de máximo 25 caracteres para un anuncio de ${platform}. Contexto: ${context}. Responde únicamente con el CTA, sin comillas ni explicaciones.`,
        prompt: `Como Emma, mejora esta descripción de producto para generar mejores anuncios de ${platform}: ${context}`
      };

      const prompt = prompts[fieldType as keyof typeof prompts];
      if (!prompt) return null;

      // Usar el endpoint real de Emma Agent
      const response = await fetch("/api/v1/agent/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          agent_id: "emma",
          message: prompt,
          context: {
            platform: platform,
            fieldType: fieldType,
            task: "generate_specific_field"
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Error generating ${fieldType}: ${response.status}`);
      }

      const result = await response.json();
      const generatedContent = result.response || result.content || "";

      // Limpiar la respuesta de comillas y texto extra
      return generatedContent.replace(/['"]/g, '').trim();
    } catch (error) {
      console.error(`Error generating ${fieldType}:`, error);
      return null;
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      sender: "user",
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsTyping(true);

    try {
      // Crear contexto específico para anuncios con información del estado actual
      const adContext = {
        platform: platform,
        currentPrompt: currentPrompt,
        step: "content_creation",
        capabilities: [
          "generate_headlines",
          "create_punchlines",
          "suggest_ctas",
          "optimize_descriptions",
          "populate_form_fields"
        ]
      };

      // Usar el endpoint real de Emma Agent
      const response = await fetch("/api/v1/agent/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          agent_id: "emma",
          message: `Como Emma, asistente especializada en creación de anuncios para ${platform}:

CONTEXTO ACTUAL:
- Plataforma: ${platform}
- Descripción del producto: ${currentPrompt || "No proporcionada aún"}
- Paso: Creación de contenido para anuncios

MENSAJE DEL USUARIO: ${inputMessage}

CAPACIDADES ESPECIALES:
- Puedo generar headlines, punchlines y CTAs automáticamente
- Puedo llenar los campos del formulario directamente
- Soy experta en marketing para ${platform}

Responde como Emma, siendo específica, práctica y enfocada en conversión. Si el usuario pide generar contenido específico (headline, punchline, CTA), hazlo directamente.`,
          context: adContext
        }),
      });

      if (!response.ok) {
        throw new Error(`Error en la comunicación con Emma: ${response.status}`);
      }

      const result = await response.json();

      // Extraer la respuesta del agente real
      const emmaResponseText = result.response || result.content || "Lo siento, no pude procesar tu solicitud.";

      // Detectar si Emma debe llenar campos automáticamente
      await handleAutomaticFieldPopulation(inputMessage, emmaResponseText);

      // Generar sugerencias específicas basadas en la respuesta
      const suggestions = generateContextualSuggestions(inputMessage, platform, emmaResponseText);

      const emmaResponse: Message = {
        id: `emma-${Date.now()}`,
        sender: "emma",
        content: emmaResponseText,
        timestamp: new Date(),
        suggestions: suggestions
      };

      setMessages(prev => [...prev, emmaResponse]);
    } catch (error) {
      console.error("Error al comunicarse con Emma:", error);

      // Fallback más inteligente - intentar con el servicio de contenido
      try {
        const fallbackResponse = await contentApiService.generateContent({
          prompt: `Como Emma, asistente de marketing para ${platform}, responde a: ${inputMessage}`,
          contentType: "assistant_response",
          maxTokens: 150,
          temperature: 0.8
        });

        const emmaResponse: Message = {
          id: `emma-fallback-${Date.now()}`,
          sender: "emma",
          content: fallbackResponse.content,
          timestamp: new Date(),
          suggestions: generateContextualSuggestions(inputMessage, platform, fallbackResponse.content)
        };

        setMessages(prev => [...prev, emmaResponse]);
      } catch (fallbackError) {
        console.error("Error en fallback:", fallbackError);

        // Último recurso: respuesta básica pero inteligente
        const basicResponse: Message = {
          id: `emma-basic-${Date.now()}`,
          sender: "emma",
          content: `Entiendo que quieres trabajar en tu anuncio de ${platform}. Aunque tengo problemas técnicos temporales, puedo ayudarte. ¿Podrías ser más específico sobre qué necesitas? Por ejemplo: "genera un headline" o "crea un punchline".`,
          timestamp: new Date(),
          suggestions: [
            "Generar headline atractivo",
            "Crear punchline persuasivo",
            "Añadir CTA efectivo",
            "Optimizar descripción"
          ]
        };

        setMessages(prev => [...prev, basicResponse]);
      }
    } finally {
      setIsTyping(false);
    }
  };

  const generateEmmaResponse = (userInput: string, platform: string): Message => {
    const input = userInput.toLowerCase();

    // Detectar tipo de negocio/producto
    const businessTypes = {
      suplementos: ['suplemento', 'proteina', 'vitamina', 'fitness', 'gym', 'nutricion'],
      tecnologia: ['tech', 'software', 'app', 'digital', 'tecnologia'],
      moda: ['ropa', 'fashion', 'moda', 'vestido', 'zapatos'],
      comida: ['restaurante', 'comida', 'food', 'cocina', 'chef'],
      servicios: ['servicio', 'consulta', 'asesor', 'profesional']
    };

    let detectedBusiness = '';
    for (const [business, keywords] of Object.entries(businessTypes)) {
      if (keywords.some(keyword => input.includes(keyword))) {
        detectedBusiness = business;
        break;
      }
    }

    // Respuestas específicas por tipo de negocio
    const businessResponses = {
      suplementos: [
        `¡Perfecto! Para una tienda de suplementos en ${platform}, te sugiero: "${generateSupplementPrompt(platform)}". Los anuncios de suplementos funcionan mejor con antes/después, testimonios y beneficios claros.`,
        `Excelente nicho. Para suplementos, enfócate en los resultados y la transformación. Te recomiendo: "${generateSupplementPrompt(platform)}"`,
        `¡Genial! Los suplementos venden muy bien online. Para ${platform}, prueba: "${generateSupplementPrompt(platform)}". Incluye siempre beneficios específicos y llamadas a la acción urgentes.`
      ],
      general: [
        `¡Excelente idea! Para ${platform}, te recomiendo enfocarte en elementos visuales llamativos y un mensaje claro.`,
        `Perfecto. En ${platform} funciona muy bien el contenido que genera emoción y conexión con la audiencia.`,
        `Me gusta tu enfoque. Para ${platform}, asegúrate de incluir un call-to-action claro y atractivo.`
      ]
    };

    const isPromptRequest = input.includes('prompt') || input.includes('idea') || input.includes('crear') || input.includes('anuncio');

    let responseArray;
    if (detectedBusiness && businessResponses[detectedBusiness as keyof typeof businessResponses]) {
      responseArray = businessResponses[detectedBusiness as keyof typeof businessResponses] as string[];
    } else if (isPromptRequest) {
      responseArray = [
        `Para ${platform}, te sugiero: "${generatePromptSuggestion(userInput, platform)}". ¿Te parece bien?`,
        `Basándome en tu idea, creo que esto funcionaría mejor: "${generatePromptSuggestion(userInput, platform)}"`,
        `Para maximizar el engagement en ${platform}, prueba: "${generatePromptSuggestion(userInput, platform)}"`
      ];
    } else {
      responseArray = businessResponses.general;
    }

    const randomResponse = responseArray[Math.floor(Math.random() * responseArray.length)];

    // Sugerencias específicas por tipo de negocio
    let suggestions = [
      "Sugerir prompt optimizado",
      "Recomendar tamaño ideal",
      "Analizar competencia",
      "Optimizar para conversión"
    ];

    if (detectedBusiness === 'suplementos') {
      suggestions = [
        "Crear anuncio antes/después",
        "Destacar beneficios clave",
        "Añadir testimonios",
        "Optimizar para fitness"
      ];
    }

    return {
      id: `emma-${Date.now()}`,
      sender: "emma",
      content: randomResponse,
      timestamp: new Date(),
      suggestions: isPromptRequest ? [] : suggestions
    };
  };

  const generateSupplementPrompt = (platform: string): string => {
    const supplementPrompts = {
      display: "Anuncio profesional de suplementos fitness, producto destacado con envase atractivo, persona atlética en segundo plano, colores energéticos azul y naranja, texto 'TRANSFORMA TU CUERPO', antes y después sutil, call-to-action 'COMPRA AHORA'",
      facebook: "Anuncio de suplementos para Facebook, producto de proteína destacado, atleta musculoso, gym en fondo, colores vibrantes, texto promocional '30% OFF', testimonial visible, diseño comercial profesional",
      instagram: "Post estético de suplementos, producto lifestyle integrado, persona fit tomando batido, iluminación natural, colores Instagram trending, ambiente motivacional y saludable",
      linkedin: "Anuncio profesional de suplementos corporativos, ejecutivo saludable, oficina moderna, producto de vitaminas, colores sobrios, mensaje de productividad y bienestar",
      youtube: "Thumbnail de suplementos llamativo, transformación corporal visible, producto destacado, colores contrastantes rojo y amarillo, texto grande 'RESULTADOS REALES', expresión de éxito"
    };

    return supplementPrompts[platform as keyof typeof supplementPrompts] || supplementPrompts.display;
  };

  const generateContextualSuggestions = (userInput: string, platform: string, aiResponse: string): string[] => {
    const input = userInput.toLowerCase();
    const response = aiResponse.toLowerCase();

    // Sugerencias específicas por tipo de negocio detectado
    if (input.includes('suplemento') || input.includes('proteina') || input.includes('fitness') || input.includes('gym')) {
      return [
        "Generar headline fitness",
        "Crear punchline motivacional",
        "Añadir CTA de acción",
        "Optimizar prompt para fitness"
      ];
    }

    if (input.includes('ropa') || input.includes('moda') || input.includes('fashion')) {
      return [
        "Generar headline de moda",
        "Crear punchline estético",
        "Añadir CTA de compra",
        "Optimizar prompt lifestyle"
      ];
    }

    if (input.includes('comida') || input.includes('restaurante') || input.includes('food')) {
      return [
        "Generar headline gastronómico",
        "Crear punchline apetitoso",
        "Añadir CTA de pedido",
        "Optimizar prompt culinario"
      ];
    }

    if (input.includes('servicio') || input.includes('consulta') || input.includes('profesional')) {
      return [
        "Generar headline profesional",
        "Crear punchline de confianza",
        "Añadir CTA de contacto",
        "Optimizar prompt B2B"
      ];
    }

    // Sugerencias generales por plataforma
    const platformSuggestions = {
      facebook: [
        "Generar headline viral",
        "Crear punchline persuasivo",
        "Añadir CTA urgente",
        "Optimizar prompt para feed"
      ],
      instagram: [
        "Generar headline estético",
        "Crear punchline visual",
        "Añadir CTA de engagement",
        "Optimizar prompt para stories"
      ],
      linkedin: [
        "Generar headline profesional",
        "Crear punchline B2B",
        "Añadir CTA de contacto",
        "Optimizar prompt corporativo"
      ],
      youtube: [
        "Generar headline llamativo",
        "Crear punchline de click",
        "Añadir CTA de suscripción",
        "Optimizar prompt para video"
      ],
      display: [
        "Generar headline directo",
        "Crear punchline claro",
        "Añadir CTA de acción",
        "Optimizar prompt para banner"
      ]
    };

    return platformSuggestions[platform as keyof typeof platformSuggestions] || platformSuggestions.display;
  };

  const generatePromptSuggestion = (userInput: string, platform: string): string => {
    const prompts = {
      facebook: "Anuncio profesional para Facebook con producto destacado, iluminación comercial, fondo limpio, colores vibrantes, texto promocional claro",
      instagram: "Post de Instagram estético, producto lifestyle, iluminación natural, composición minimalista, colores trending, muy visual",
      linkedin: "Anuncio corporativo profesional, ambiente de oficina moderna, personas en traje, colores sobrios, mensaje B2B claro",
      youtube: "Thumbnail llamativo para YouTube, expresión facial expresiva, colores contrastantes, texto grande legible, elementos gráficos"
    };

    return prompts[platform as keyof typeof prompts] || "Anuncio profesional optimizado para la plataforma";
  };

  const handleSuggestionClick = async (suggestion: string) => {
    if (suggestion.includes("prompt") || suggestion.includes("anuncio")) {
      // Generar prompt específico basado en el contexto
      let generatedPrompt = "";

      if (suggestion.includes("antes/después")) {
        generatedPrompt = generateSupplementPrompt(platform);
      } else if (suggestion.includes("beneficios")) {
        generatedPrompt = `Anuncio de suplementos para ${platform}, enfoque en beneficios: más energía, mejor rendimiento, recuperación rápida, producto destacado, colores energéticos, call-to-action claro`;
      } else if (suggestion.includes("testimonios")) {
        generatedPrompt = `Anuncio con testimonial para ${platform}, cliente satisfecho con suplementos, antes y después real, quote destacado, producto visible, diseño confiable y profesional`;
      } else if (suggestion.includes("fitness")) {
        generatedPrompt = `Anuncio fitness de suplementos para ${platform}, atleta entrenando, producto integrado, ambiente de gym, colores dinámicos, mensaje motivacional, resultados visibles`;
      } else {
        generatedPrompt = generatePromptSuggestion("", platform);
      }

      if (onPromptSuggestion) {
        onPromptSuggestion(generatedPrompt);
      }
    } else if (suggestion.includes("headline") || suggestion.includes("título")) {
      // Generar headline específico
      const generatedHeadline = await generateSpecificField('headline', currentPrompt || suggestion);
      if (generatedHeadline && onHeadlineSuggestion) {
        onHeadlineSuggestion(generatedHeadline);
      }
    } else if (suggestion.includes("punchline") || suggestion.includes("subtítulo")) {
      // Generar punchline específico
      const generatedPunchline = await generateSpecificField('punchline', currentPrompt || suggestion);
      if (generatedPunchline && onPunchlineSuggestion) {
        onPunchlineSuggestion(generatedPunchline);
      }
    } else if (suggestion.includes("cta") || suggestion.includes("botón")) {
      // Generar CTA específico
      const generatedCta = await generateSpecificField('cta', currentPrompt || suggestion);
      if (generatedCta && onCtaSuggestion) {
        onCtaSuggestion(generatedCta);
      }
    } else if (suggestion.includes("tamaño") && onSizeRecommendation) {
      // Recomendar tamaño óptimo para la plataforma
      const optimalSizes = {
        facebook: "1200x1200",
        instagram: "1080x1080",
        linkedin: "1200x627",
        youtube: "1280x720",
        display: "728x90"
      };
      onSizeRecommendation(optimalSizes[platform as keyof typeof optimalSizes] || "1200x1200");
    }

    setInputMessage(suggestion);
  };

  if (!isOpen) {
    return (
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className={`fixed bottom-6 right-6 z-50 ${className}`}
      >
        <Button
          onClick={() => setIsOpen(true)}
          className="w-16 h-16 rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 shadow-lg"
        >
          <div className="relative">
            <Avatar className="w-10 h-10">
              <AvatarImage src={EmmaProfile} alt="Emma AI" />
              <AvatarFallback>E</AvatarFallback>
            </Avatar>
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
          </div>
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      className={`fixed bottom-6 right-6 z-50 ${className}`}
    >
      <Card className={`w-80 shadow-2xl border-0 bg-white/95 backdrop-blur-sm ${isMinimized ? 'h-14' : 'h-[450px]'} transition-all duration-300`}>
        <CardHeader className="p-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8 border-2 border-white/50">
                <AvatarImage src={EmmaProfile} alt="Emma AI" />
                <AvatarFallback>E</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold text-sm">Emma AI</h3>
                <p className="text-xs text-white/80">Asistente de Anuncios</p>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white hover:bg-white/20 w-8 h-8 p-0"
              >
                {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-white/20 w-8 h-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-0 flex flex-col h-[396px]">
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`max-w-[80%] p-3 rounded-lg ${
                      message.sender === "user"
                        ? "bg-[#3018ef] text-white"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    {message.suggestions && message.suggestions.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {message.suggestions.map((suggestion, index) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="text-xs h-6 px-2 mr-1 mb-1"
                          >
                            {suggestion}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 p-3 rounded-lg">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Pregúntame sobre tu anuncio..."
                  onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isTyping}
                  className="bg-[#3018ef] hover:bg-[#3018ef]/90"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
              <div className="mt-2 text-xs text-gray-500 flex items-center">
                <Sparkles className="w-3 h-3 mr-1" />
                Powered by Emma AI
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </motion.div>
  );
};

export default EmmaAdAssistant;
