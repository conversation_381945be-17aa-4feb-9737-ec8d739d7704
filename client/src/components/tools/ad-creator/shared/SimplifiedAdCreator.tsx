/**
 * Simplified Ad Creator - Results-Focused Flow
 * Clean 3-step process: Size → Content → Generate
 */

import { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Wand2, Download, Heart, Sparkles, Check } from "lucide-react";

// Import shared types and services
import { generateAd, type AdGenerationOptions } from "@/lib/services/ad-generation";
import { validateImageFile } from "@/lib/utils/file-validation";
import { useSavedAds } from "@/hooks/use-saved-ads";
import { GeneratedAd, PlatformConfig } from "@/types/ad-creator-types";
import EmmaAdAssistant from "@/components/tools/ad-creator/EmmaAdAssistant";


// Step types for the simplified flow
type Step = "size" | "content" | "results";

// Size options for each platform
const PLATFORM_SIZES = {
  facebook: [
    { name: "Post Cuadrado", dimensions: "1080x1080", desc: "Ideal para feed", icon: "📱" },
    { name: "Historia", dimensions: "1080x1920", desc: "Stories verticales", icon: "📲" },
    { name: "Portada", dimensions: "1200x628", desc: "Anuncios de enlace", icon: "🖼️" }
  ],
  instagram: [
    { name: "Post Cuadrado", dimensions: "1080x1080", desc: "Feed principal", icon: "📱" },
    { name: "Historia", dimensions: "1080x1920", desc: "Stories", icon: "📲" },
    { name: "Carrusel", dimensions: "1080x1080", desc: "Múltiples imágenes", icon: "🎠" }
  ],
  linkedin: [
    { name: "Post Cuadrado", dimensions: "1080x1080", desc: "Feed profesional", icon: "💼" },
    { name: "Banner", dimensions: "1200x628", desc: "Anuncios patrocinados", icon: "🖼️" },
    { name: "Historia", dimensions: "1080x1920", desc: "LinkedIn Stories", icon: "📲" }
  ],
  google: [
    { name: "Banner Mediano", dimensions: "300x250", desc: "Display estándar", icon: "🖥️" },
    { name: "Leaderboard", dimensions: "728x90", desc: "Banner superior", icon: "📊" },
    { name: "Rascacielos", dimensions: "160x600", desc: "Banner lateral", icon: "🏢" }
  ]
};

interface SimplifiedAdCreatorProps {
  platform: string;
  config: PlatformConfig;
  onBack: () => void;
}

export function SimplifiedAdCreator({ platform, config, onBack }: SimplifiedAdCreatorProps) {
  // State management
  const [currentStep, setCurrentStep] = useState<Step>("size");
  const [selectedSize, setSelectedSize] = useState<any>(null);
  const [prompt, setPrompt] = useState("");
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // New text fields state
  const [headline, setHeadline] = useState("");
  const [punchline, setPunchline] = useState("");
  const [cta, setCta] = useState("");

  // Loading states for AI generation
  const [isGeneratingHeadline, setIsGeneratingHeadline] = useState(false);
  const [isGeneratingPunchline, setIsGeneratingPunchline] = useState(false);
  const [isGeneratingCta, setIsGeneratingCta] = useState(false);

  // Refs and hooks
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { saveAd } = useSavedAds();

  // Get platform sizes
  const platformSizes = PLATFORM_SIZES[platform as keyof typeof PLATFORM_SIZES] || PLATFORM_SIZES.facebook;

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setUploadedImage(file);
  };

  // Real AI text generation functions using LLM
  const handleGenerateHeadlines = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor describe tu producto antes de generar headlines",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingHeadline(true);
    try {
      const response = await fetch("/api/v1/agent/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          agent_id: "emma",
          message: `Como Emma, genera SOLO un headline atractivo y persuasivo para un anuncio de ${platform}.

DESCRIPCIÓN DEL PRODUCTO: "${prompt}"

REQUISITOS:
- Máximo 40 caracteres
- Llamativo y directo
- Enfocado en beneficios
- Optimizado para conversión en ${platform}

Responde únicamente con el headline, sin comillas ni explicaciones adicionales.`,
          context: {
            platform: platform,
            task: "generate_headline",
            productDescription: prompt
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Error generating headline: ${response.status}`);
      }

      const result = await response.json();
      const generatedHeadline = (result.response || result.content || "").replace(/['"]/g, '').trim();
      setHeadline(generatedHeadline);
      toast({
        title: "Headline generado",
        description: "Se ha generado un nuevo headline con IA",
      });
    } catch (error) {
      console.error("Error generating headline:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando el headline. Intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingHeadline(false);
    }
  };

  const handleGeneratePunchlines = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor describe tu producto antes de generar punchlines",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingPunchline(true);
    try {
      const response = await fetch("/api/v1/agent/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          agent_id: "emma",
          message: `Como Emma, genera SOLO un punchline persuasivo para un anuncio de ${platform}.

DESCRIPCIÓN DEL PRODUCTO: "${prompt}"

REQUISITOS:
- Máximo 40 caracteres
- Convincente y emocional
- Que genere confianza
- Que destaque beneficios únicos para ${platform}

Responde únicamente con el punchline, sin comillas ni explicaciones adicionales.`,
          context: {
            platform: platform,
            task: "generate_punchline",
            productDescription: prompt
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Error generating punchline: ${response.status}`);
      }

      const result = await response.json();
      const generatedPunchline = (result.response || result.content || "").replace(/['"]/g, '').trim();
      setPunchline(generatedPunchline);
      toast({
        title: "Punchline generado",
        description: "Se ha generado un nuevo punchline con IA",
      });
    } catch (error) {
      console.error("Error generating punchline:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando el punchline. Intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPunchline(false);
    }
  };

  const handleGenerateCTAs = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor describe tu producto antes de generar CTAs",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingCta(true);
    try {
      const response = await fetch("/api/v1/agent/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          agent_id: "emma",
          message: `Como Emma, genera SOLO un call-to-action (CTA) efectivo para un anuncio de ${platform}.

DESCRIPCIÓN DEL PRODUCTO: "${prompt}"

REQUISITOS:
- Máximo 25 caracteres
- Accionable y urgente
- Que motive a hacer clic
- Específico para ${platform}

Responde únicamente con el CTA, sin comillas ni explicaciones adicionales.`,
          context: {
            platform: platform,
            task: "generate_cta",
            productDescription: prompt
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Error generating CTA: ${response.status}`);
      }

      const result = await response.json();
      const generatedCta = (result.response || result.content || "").replace(/['"]/g, '').trim();
      setCta(generatedCta);
      toast({
        title: "CTA generado",
        description: "Se ha generado un nuevo CTA con IA",
      });
    } catch (error) {
      console.error("Error generating CTA:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando el CTA. Intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingCta(false);
    }
  };

  // Generate ads
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor describe tu producto o servicio",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setCurrentStep("results");
    
    try {
      // Generate 6 variations
      const variations = [];
      for (let i = 0; i < 6; i++) {
        const options: AdGenerationOptions = {
          prompt: `${config.promptPrefix || ""} ${prompt}`,
          size: selectedSize.dimensions,
        };

        const result = await generateAd(options);
        if (result.success && result.image_url) {
          variations.push({
            id: `${Date.now()}-${i}`,
            image_url: result.image_url,
            prompt,
            revised_prompt: result.revised_prompt,
            response_id: result.response_id,
            metadata: {
              size: selectedSize.dimensions,
              platform,
              variation: i + 1
            },
            timestamp: Date.now()
          });
        }
      }

      setGeneratedAds(variations);
      
      toast({
        title: "¡Anuncios generados!",
        description: `Se generaron ${variations.length} variaciones exitosamente.`,
      });
    } catch (error) {
      console.error("Error generating ads:", error);
      toast({
        title: "Error al generar",
        description: "Hubo un problema generando los anuncios. Intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Save ad
  const handleSave = (ad: GeneratedAd) => {
    saveAd({ ...ad, isFavorite: false });
    toast({
      title: "Anuncio guardado",
      description: "El anuncio se guardó en tu colección.",
    });
  };

  // Download ad
  const handleDownload = (imageUrl: string, adId: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${platform}-ad-${adId}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Descarga iniciada",
      description: "Tu anuncio se está descargando",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      {/* Premium Header with Glassmorphism */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-white/20 px-6 py-6 sticky top-0 z-50">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-6">
            <button
              onClick={onBack}
              className="p-3 hover:bg-white/60 rounded-xl transition-all duration-300 hover:scale-105 backdrop-blur-sm border border-white/20"
            >
              <ArrowLeft className="w-5 h-5 text-slate-700" />
            </button>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                Crear Anuncio para {config.name}
              </h1>
              <p className="text-sm text-slate-600 font-medium">
                ✨ Proceso simplificado con IA avanzada
              </p>
            </div>
          </div>

          {/* Premium Progress Steps */}
          <div className="flex items-center gap-3">
            <div className={`flex items-center gap-3 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
              currentStep === "size" ? "bg-gradient-to-r from-[#3018ef] to-[#4c51bf] text-white shadow-lg shadow-blue-500/25" :
              selectedSize ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25" : "bg-white/60 text-slate-500 backdrop-blur-sm border border-white/20"
            }`}>
              {selectedSize ? <Check className="w-4 h-4" /> : <span className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center text-xs">1</span>}
              Tamaño
            </div>
            <div className={`flex items-center gap-3 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
              currentStep === "content" ? "bg-gradient-to-r from-[#3018ef] to-[#4c51bf] text-white shadow-lg shadow-blue-500/25" :
              prompt ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25" : "bg-white/60 text-slate-500 backdrop-blur-sm border border-white/20"
            }`}>
              {prompt ? <Check className="w-4 h-4" /> : <span className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center text-xs">2</span>}
              Contenido
            </div>
            <div className={`flex items-center gap-3 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
              currentStep === "results" ? "bg-gradient-to-r from-[#3018ef] to-[#4c51bf] text-white shadow-lg shadow-blue-500/25" : "bg-white/60 text-slate-500 backdrop-blur-sm border border-white/20"
            }`}>
              {generatedAds.length > 0 ? <Check className="w-4 h-4" /> : <span className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center text-xs">3</span>}
              Generar
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Step 1: Premium Size Selection */}
        {currentStep === "size" && (
          <div className="space-y-12">
            <div className="text-center">
              <h2 className="text-4xl font-bold mb-4">
                <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  Selecciona el Tamaño Creativo
                </span>
              </h2>
              <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
                Elige el formato perfecto para maximizar el impacto de tu anuncio
              </p>
              <div className="mt-6 inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50">
                <Sparkles className="w-4 h-4 text-[#3018ef]" />
                <span className="text-sm font-medium text-slate-700">Optimizado para {config.name}</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {platformSizes.map((size, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setSelectedSize(size);
                    setCurrentStep("content");
                  }}
                  className="group relative p-8 bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 hover:border-[#3018ef]/30 hover:shadow-2xl hover:shadow-[#3018ef]/10 transition-all duration-500 hover:scale-105 hover:-translate-y-2"
                >
                  {/* Premium gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef]/5 to-[#dd3a5a]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Content */}
                  <div className="relative z-10">
                    <div className="text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                      {size.icon}
                    </div>
                    <h3 className="text-xl font-bold text-slate-900 mb-3 group-hover:text-[#3018ef] transition-colors">
                      {size.name}
                    </h3>
                    <p className="text-slate-600 mb-4 leading-relaxed">
                      {size.desc}
                    </p>
                    <div className="inline-flex items-center gap-2 px-3 py-1 bg-slate-100 rounded-full">
                      <span className="text-xs font-mono text-slate-700">{size.dimensions}</span>
                    </div>
                  </div>

                  {/* Premium selection indicator */}
                  <div className="absolute top-4 right-4 w-6 h-6 rounded-full border-2 border-slate-300 group-hover:border-[#3018ef] group-hover:bg-[#3018ef] transition-all duration-300">
                    <div className="w-full h-full rounded-full bg-[#3018ef] scale-0 group-hover:scale-50 transition-transform duration-300" />
                  </div>
                </button>
              ))}
            </div>

            {/* Premium features showcase */}
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#3018ef] to-[#4c51bf] rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-slate-900 mb-2">IA Avanzada</h4>
                  <p className="text-sm text-slate-600">Generación con modelos de última generación</p>
                </div>
                <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#dd3a5a] to-[#e55a7a] rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Download className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-slate-900 mb-2">Alta Resolución</h4>
                  <p className="text-sm text-slate-600">Imágenes optimizadas para cada plataforma</p>
                </div>
                <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-slate-900 mb-2">Múltiples Variaciones</h4>
                  <p className="text-sm text-slate-600">6 opciones únicas por generación</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Enhanced Content Input */}
        {currentStep === "content" && (
          <div className="max-w-7xl mx-auto px-4">
            {/* Header Section */}
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-3">
                <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  Crea Contenido Profesional
                </span>
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto mb-6">
                Define cada elemento de tu anuncio con precisión y genera textos optimizados con IA
              </p>

              {/* Emma will help via the floating chatbot */}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Panel - Content Inputs */}
              <div className="space-y-6">
                {/* Upload Product Image */}
                <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm">📷</span>
                    </div>
                    <label className="text-lg font-bold text-slate-900">
                      Imagen del producto
                    </label>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="w-full p-4 border-2 border-dashed border-slate-300 rounded-xl text-slate-600 hover:border-[#3018ef] hover:text-[#3018ef] hover:bg-[#3018ef]/5 transition-all duration-300 flex items-center justify-center gap-3 font-medium"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm">📷</span>
                    </div>
                    {uploadedImage ? "Cambiar imagen del producto" : "Subir imagen del producto"}
                  </button>
                  {uploadedImage && (
                    <div className="mt-3 text-center">
                      <p className="text-sm text-green-600 flex items-center justify-center gap-2">
                        <Check className="w-4 h-4" />
                        Imagen subida exitosamente
                      </p>
                    </div>
                  )}
                </div>

                {/* Product Description */}
                <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm font-bold">📝</span>
                    </div>
                    <label className="text-lg font-bold text-slate-900">
                      Descripción del producto
                    </label>
                  </div>
                  <div className="relative">
                    <textarea
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      placeholder="Describe tu producto o servicio en detalle. Incluye características, beneficios y público objetivo..."
                      className="w-full h-32 p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-300 resize-none text-base bg-white/80 backdrop-blur-sm hover:border-slate-300"
                      maxLength={300}
                    />
                    <div className={`absolute bottom-3 right-3 text-xs px-2 py-1 rounded-full ${
                      prompt.length > 250 ? 'bg-red-100 text-red-600' :
                      prompt.length > 150 ? 'bg-yellow-100 text-yellow-600' :
                      'bg-green-100 text-green-600'
                    }`}>
                      {prompt.length}/300
                    </div>
                  </div>
                  <div className="mt-3 flex items-center gap-2 text-sm text-slate-600">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Mientras más detalles proporciones, mejor será el resultado
                  </div>
                </div>

                {/* On Image Texts */}
                <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-[#dd3a5a] to-[#e55a7a] rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm font-bold">T</span>
                      </div>
                      <h4 className="text-xl font-bold text-slate-900">On Image Texts</h4>
                    </div>
                    <button
                      onClick={() => {
                        setHeadline("");
                        setPunchline("");
                        setCta("");
                      }}
                      className="text-slate-400 hover:text-slate-600 text-sm flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-slate-100 transition-colors"
                    >
                      🧹 Clean Texts
                    </button>
                  </div>

                  <div className="space-y-5">
                    {/* Main Headline */}
                    <div className="group">
                      <div className="flex items-center justify-between mb-3">
                        <label className="text-sm font-medium text-slate-700 flex items-center gap-2">
                          <div className="w-2 h-2 bg-[#3018ef] rounded-full"></div>
                          Your main headline here!
                        </label>
                        <div className="flex items-center gap-3">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            headline.length > 35 ? 'bg-red-100 text-red-600' :
                            headline.length > 25 ? 'bg-yellow-100 text-yellow-600' :
                            'bg-green-100 text-green-600'
                          }`}>
                            {headline.length}/40
                          </span>
                          <button
                            onClick={handleGenerateHeadlines}
                            disabled={!prompt.trim() || isGeneratingHeadline}
                            className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] rounded-lg flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 shadow-md"
                          >
                            {isGeneratingHeadline ? (
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                            ) : (
                              <Wand2 className="w-4 h-4 text-white" />
                            )}
                          </button>
                        </div>
                      </div>
                      <div className="relative">
                        <input
                          type="text"
                          value={headline}
                          onChange={(e) => setHeadline(e.target.value)}
                          placeholder="Your main headline here!"
                          className="w-full p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-[#3018ef]/20 focus:border-[#3018ef] transition-all duration-300 text-base font-medium bg-white/80 backdrop-blur-sm hover:border-slate-300"
                          maxLength={40}
                        />
                        {headline && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <Check className="w-5 h-5 text-green-500" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Punchline */}
                    <div className="group">
                      <div className="flex items-center justify-between mb-3">
                        <label className="text-sm font-medium text-slate-700 flex items-center gap-2">
                          <div className="w-2 h-2 bg-[#dd3a5a] rounded-full"></div>
                          Your punchline is here!
                        </label>
                        <div className="flex items-center gap-3">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            punchline.length > 35 ? 'bg-red-100 text-red-600' :
                            punchline.length > 25 ? 'bg-yellow-100 text-yellow-600' :
                            'bg-green-100 text-green-600'
                          }`}>
                            {punchline.length}/40
                          </span>
                          <button
                            onClick={handleGeneratePunchlines}
                            disabled={!prompt.trim() || isGeneratingPunchline}
                            className="w-8 h-8 bg-gradient-to-r from-[#dd3a5a] to-[#e55a7a] hover:from-[#c73650] hover:to-[#d94d6e] rounded-lg flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 shadow-md"
                          >
                            {isGeneratingPunchline ? (
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                            ) : (
                              <Wand2 className="w-4 h-4 text-white" />
                            )}
                          </button>
                        </div>
                      </div>
                      <div className="relative">
                        <input
                          type="text"
                          value={punchline}
                          onChange={(e) => setPunchline(e.target.value)}
                          placeholder="Your punchline is here!"
                          className="w-full p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-[#dd3a5a]/20 focus:border-[#dd3a5a] transition-all duration-300 text-base font-medium bg-white/80 backdrop-blur-sm hover:border-slate-300"
                          maxLength={40}
                        />
                        {punchline && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <Check className="w-5 h-5 text-green-500" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* CTA */}
                    <div className="group">
                      <div className="flex items-center justify-between mb-3">
                        <label className="text-sm font-medium text-slate-700 flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          Call to action text here!
                        </label>
                        <div className="flex items-center gap-3">
                          <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full">Optional</span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            cta.length > 20 ? 'bg-red-100 text-red-600' :
                            cta.length > 15 ? 'bg-yellow-100 text-yellow-600' :
                            'bg-green-100 text-green-600'
                          }`}>
                            {cta.length}/25
                          </span>
                          <button
                            onClick={handleGenerateCTAs}
                            disabled={!prompt.trim() || isGeneratingCta}
                            className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 rounded-lg flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-110 shadow-md"
                          >
                            {isGeneratingCta ? (
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                            ) : (
                              <Wand2 className="w-4 h-4 text-white" />
                            )}
                          </button>
                        </div>
                      </div>
                      <div className="relative">
                        <input
                          type="text"
                          value={cta}
                          onChange={(e) => setCta(e.target.value)}
                          placeholder="Call to action text here!"
                          className="w-full p-4 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-green-500/20 focus:border-green-500 transition-all duration-300 text-base font-medium bg-white/80 backdrop-blur-sm hover:border-slate-300"
                          maxLength={25}
                        />
                        {cta && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <Check className="w-5 h-5 text-green-500" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Emma provides feedback via the floating chatbot */}

                {/* Next Step Button */}
                <div className="relative">
                  <button
                    onClick={handleGenerate}
                    disabled={!prompt.trim() || isGenerating}
                    className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#2516d6] hover:to-[#c73650] text-white py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center gap-3 shadow-lg"
                  >
                    {isGenerating ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="w-5 h-5" />
                        Next Step →
                      </>
                    )}
                  </button>
                  {!prompt.trim() && (
                    <div className="absolute -bottom-8 left-0 text-xs text-red-500 flex items-center gap-1">
                      <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                      Descripción del producto requerida
                    </div>
                  )}
                </div>
              </div>

              {/* Right Panel - Enhanced Preview */}
              <div className="space-y-6">
                {/* Preview Header */}
                <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-4 rounded-2xl border border-slate-200/60 shadow-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm font-bold">👁️</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-slate-900">Live Preview</h4>
                      <p className="text-sm text-slate-600">See how your ad will look</p>
                    </div>
                  </div>
                </div>

                {/* Main Preview */}
                <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="aspect-square bg-gradient-to-br from-[#3018ef] via-blue-500 to-[#dd3a5a] rounded-2xl p-8 text-white relative overflow-hidden shadow-2xl">
                    {/* Animated Background */}
                    <div className="absolute inset-0 opacity-20">
                      <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full blur-3xl animate-pulse"></div>
                      <div className="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full blur-2xl animate-pulse delay-1000"></div>
                    </div>

                    {/* Preview Content */}
                    <div className="relative z-10 h-full flex flex-col">
                      <div className="text-center mb-6">
                        <h3 className={`text-2xl font-bold mb-3 transition-all duration-300 ${
                          headline ? 'opacity-100 scale-100' : 'opacity-60 scale-95'
                        }`}>
                          {headline || "Your main headline here!"}
                        </h3>
                        <p className={`text-lg leading-relaxed transition-all duration-300 ${
                          punchline ? 'opacity-100 scale-100' : 'opacity-60 scale-95'
                        }`}>
                          {punchline || "Your punchline is here!"}
                        </p>
                      </div>

                      <div className="flex-1 flex items-center justify-center">
                        {uploadedImage ? (
                          <div className="relative group">
                            <img
                              src={URL.createObjectURL(uploadedImage)}
                              alt="Product preview"
                              className="w-40 h-40 object-cover rounded-2xl shadow-2xl border-4 border-white/30 group-hover:scale-105 transition-transform duration-300"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                          </div>
                        ) : (
                          <div className="w-40 h-40 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border-2 border-dashed border-white/40 hover:border-white/60 transition-colors">
                            <div className="text-center">
                              <span className="text-5xl mb-2 block">📱</span>
                              <p className="text-sm opacity-80">Product Image</p>
                            </div>
                          </div>
                        )}
                      </div>

                      {(cta || !cta) && (
                        <div className="text-center">
                          <button className={`bg-white text-[#3018ef] px-8 py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 ${
                            cta ? 'opacity-100' : 'opacity-60'
                          }`}>
                            {cta || "Call to Action"} →
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Decorative Elements */}
                    <div className="absolute top-4 right-4 w-3 h-3 bg-white rounded-full opacity-60 animate-ping"></div>
                    <div className="absolute bottom-4 left-4 w-2 h-2 bg-white rounded-full opacity-40 animate-pulse delay-500"></div>
                  </div>
                </div>


              </div>
            </div>
          </div>
        )}

        {/* Step 3: Premium Results */}
        {currentStep === "results" && (
          <div className="space-y-12">
            <div className="text-center">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-4xl font-bold mb-4"
              >
                <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  🎉 Tus Anuncios Generados
                </span>
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed mb-6"
              >
                Emma ha creado 6 variaciones únicas con IA avanzada
              </motion.p>

              {/* Emma Success Message */}
              {/* Emma celebrates success via the floating chatbot */}
            </div>

            {isGenerating ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-20"
              >
                <div className="relative">
                  {/* Premium loading animation */}
                  <div className="w-24 h-24 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center mx-auto mb-8 animate-pulse">
                    <Sparkles className="w-12 h-12 text-white animate-spin" />
                  </div>

                  {/* Loading rings */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2">
                    <div className="w-32 h-32 border-4 border-[#3018ef]/20 border-t-[#3018ef] rounded-full animate-spin"></div>
                  </div>
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
                    <div className="w-28 h-28 border-4 border-[#dd3a5a]/20 border-t-[#dd3a5a] rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '3s' }}></div>
                  </div>
                </div>

                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="space-y-4"
                >
                  <h3 className="text-2xl font-bold text-slate-900">Emma está trabajando su magia ✨</h3>
                  <p className="text-lg text-slate-600">Generando 6 anuncios únicos con IA avanzada...</p>
                  <div className="flex items-center justify-center gap-6 mt-8">
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <div className="w-2 h-2 bg-[#3018ef] rounded-full animate-pulse"></div>
                      Analizando contenido
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <div className="w-2 h-2 bg-[#dd3a5a] rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                      Creando diseños
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                      Optimizando calidad
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ) : (
              <div className="max-w-7xl mx-auto">
                <AnimatePresence>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {generatedAds.map((ad, index) => (
                      <motion.div
                        key={ad.id}
                        initial={{ opacity: 0, y: 20, scale: 0.9 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ delay: index * 0.1, duration: 0.5 }}
                        whileHover={{ y: -8, scale: 1.02 }}
                        className="group bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden hover:shadow-2xl hover:shadow-[#3018ef]/10 transition-all duration-500"
                      >
                        {/* Image container */}
                        <div className="relative overflow-hidden">
                          <img
                            src={ad.image_url}
                            alt="Generated ad"
                            className="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-500"
                          />
                          {/* Premium overlay */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                          {/* Quality badge */}
                          <div className="absolute top-4 left-4">
                            <div className="px-3 py-1 bg-gradient-to-r from-[#3018ef] to-[#4c51bf] text-white text-xs font-bold rounded-full">
                              ✨ IA Premium
                            </div>
                          </div>

                          {/* Variation number */}
                          <div className="absolute top-4 right-4">
                            <div className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-sm font-bold text-slate-700">
                              {index + 1}
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="p-6">
                          <div className="flex items-center justify-between gap-3">
                            <button
                              onClick={() => handleSave(ad)}
                              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-50 to-pink-50 hover:from-red-100 hover:to-pink-100 text-red-600 rounded-xl font-medium transition-all duration-300 hover:scale-105 group/save"
                            >
                              <Heart className="w-4 h-4 group-hover/save:scale-110 transition-transform" />
                              Guardar
                            </button>
                            <button
                              onClick={() => handleDownload(ad.image_url, ad.id)}
                              className="flex items-center gap-2 px-6 py-2 bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-[#3018ef]/25 group/download"
                            >
                              <Download className="w-4 h-4 group-hover/download:scale-110 transition-transform" />
                              Descargar
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </AnimatePresence>
              </div>
            )}

            {generatedAds.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="text-center space-y-6"
              >
                <div className="max-w-2xl mx-auto p-8 bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20">
                  <h3 className="text-xl font-bold text-slate-900 mb-4">¿Te gustaron los resultados?</h3>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={() => {
                        setCurrentStep("content");
                        setGeneratedAds([]);
                      }}
                      className="px-8 py-3 bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] text-white rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-[#3018ef]/25"
                    >
                      ✨ Generar Más Variaciones
                    </button>
                    <button
                      onClick={() => setCurrentStep("size")}
                      className="px-8 py-3 bg-gradient-to-r from-slate-100 to-slate-200 hover:from-slate-200 hover:to-slate-300 text-slate-700 rounded-xl font-semibold transition-all duration-300 hover:scale-105"
                    >
                      🔄 Cambiar Formato
                    </button>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        )}
      </div>

      {/* Floating Emma AI Assistant - Solo en paso 2 (content) */}
      {currentStep === "content" && (
        <EmmaAdAssistant
          platform={platform}
          currentPrompt={prompt}
          onPromptSuggestion={(newPrompt) => setPrompt(newPrompt)}
          onHeadlineSuggestion={(newHeadline: string) => setHeadline(newHeadline)}
          onPunchlineSuggestion={(newPunchline: string) => setPunchline(newPunchline)}
          onCtaSuggestion={(newCta: string) => setCta(newCta)}
          onSizeRecommendation={(size) => {
            // Find the size object that matches the recommended dimensions
            const matchingSize = platformSizes.find(s => s.dimensions === size);
            if (matchingSize) {
              setSelectedSize(matchingSize);
              // Emma puede sugerir cambiar el tamaño incluso desde el paso de contenido
            }
          }}
        />
      )}
    </div>
  );
}
